#ifndef MAPWIDGET_H
#define MAPWIDGET_H

#include <QGraphicsView>
#include <QGraphicsScene>
#include <QWheelEvent>
#include <QMouseEvent>
#include <QKeyEvent>
#include <QTimer>

class TileLoader;
class CoordinateConverter;
class TargetItem;

/**
 * @brief 地图显示组件
 * 
 * 基于QGraphicsView的地图显示组件，支持瓦片加载、缩放、拖拽等功能
 */
class MapWidget : public QGraphicsView
{
    Q_OBJECT

public:
    explicit MapWidget(QWidget *parent = nullptr);
    ~MapWidget();
    
    // 地图操作
    void setCenter(double latitude, double longitude);
    void setZoomLevel(int level);
    void resetView();
    
    // 目标管理
    void addTarget(const QString& targetId, double latitude, double longitude);
    void updateTarget(const QString& targetId, double latitude, double longitude);
    void removeTarget(const QString& targetId);
    
    // 获取当前状态
    QPointF getCenter() const;
    int getZoomLevel() const;

signals:
    void centerChanged(double latitude, double longitude);
    void zoomLevelChanged(int level);
    void targetClicked(const QString& targetId);

protected:
    void wheelEvent(QWheelEvent *event) override;
    void mousePressEvent(QMouseEvent *event) override;
    void mouseMoveEvent(QMouseEvent *event) override;
    void mouseReleaseEvent(QMouseEvent *event) override;
    void keyPressEvent(QKeyEvent *event) override;
    void resizeEvent(QResizeEvent *event) override;

private slots:
    void onTileLoaded(int x, int y, int z, const QPixmap& pixmap);
    void onLoadingProgress(int loaded, int total);
    void updateVisibleTiles();

private:
    void setupScene();
    void setupTileLoader();
    void updateTiles();
    void constrainView();
    void smoothZoom(double factor, const QPointF& center);

private:
    QGraphicsScene* m_scene;
    TileLoader* m_tileLoader;
    CoordinateConverter* m_converter;
    
    // 地图状态
    int m_zoomLevel;
    QPointF m_center;  // 经纬度坐标
    
    // 交互状态
    bool m_dragging;
    QPoint m_lastPanPoint;
    QTimer* m_updateTimer;
    
    // 目标管理
    QMap<QString, TargetItem*> m_targets;
    
    // 常量
    static const double ZOOM_FACTOR;
    static const int UPDATE_DELAY;
};

#endif // MAPWIDGET_H
