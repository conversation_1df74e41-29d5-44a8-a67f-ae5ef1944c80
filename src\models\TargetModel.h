#ifndef TARGETMODEL_H
#define TARGETMODEL_H

#include <QAbstractTableModel>
#include <QList>
#include "common/Target.h"

/**
 * @brief 目标数据模型
 * 
 * 为目标列表视图提供数据模型，支持MVC架构
 */
class TargetModel : public QAbstractTableModel
{
    Q_OBJECT

public:
    // 列枚举
    enum Column {
        ColumnId = 0,
        ColumnName,
        ColumnLatitude,
        ColumnLongitude,
        ColumnAltitude,
        ColumnHeading,
        ColumnSpeed,
        ColumnType,
        ColumnStatus,
        ColumnLastUpdate,
        ColumnCount
    };

    explicit TargetModel(QObject *parent = nullptr);
    ~TargetModel();
    
    // QAbstractTableModel接口实现
    int rowCount(const QModelIndex &parent = QModelIndex()) const override;
    int columnCount(const QModelIndex &parent = QModelIndex()) const override;
    
    QVariant data(const QModelIndex &index, int role = Qt::DisplayRole) const override;
    QVariant headerData(int section, Qt::Orientation orientation, int role = Qt::DisplayRole) const override;
    
    Qt::ItemFlags flags(const QModelIndex &index) const override;
    bool setData(const QModelIndex &index, const QVariant &value, int role = Qt::EditRole) override;
    
    // 目标管理
    void addTarget(Target* target);
    void updateTarget(Target* target);
    void removeTarget(const QString& targetId);
    void removeTarget(Target* target);
    void clearAllTargets();
    
    // 目标查询
    Target* getTarget(int row) const;
    Target* getTarget(const QString& targetId) const;
    int findTargetRow(const QString& targetId) const;
    int findTargetRow(Target* target) const;
    
    // 排序和过滤
    void sort(int column, Qt::SortOrder order = Qt::AscendingOrder) override;
    void setFilter(const QString& filter);
    QString getFilter() const;

signals:
    void targetSelectionChanged(const QString& targetId);

private:
    // 数据获取方法
    QVariant getDisplayData(const Target* target, int column) const;
    QVariant getAlignmentData(int column) const;
    QVariant getBackgroundData(const Target* target, int column) const;
    QVariant getForegroundData(const Target* target, int column) const;
    QVariant getDecorationData(const Target* target, int column) const;
    QVariant getToolTipData(const Target* target, int column) const;

    // 格式化方法
    QString formatCoordinate(double value, int precision = 6) const;
    QString formatSpeed(double speed) const;
    QString formatHeading(double heading) const;
    QString formatDateTime(const QDateTime& dateTime) const;
    QIcon getStatusIcon(const QString& status) const;
    QColor getStatusColor(const QString& status) const;

private:
    QList<Target*> m_targets;
    QString m_filter;
    
    // 列标题
    static const QStringList COLUMN_HEADERS;
};

#endif // TARGETMODEL_H
