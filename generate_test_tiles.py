#!/usr/bin/env python3
"""
测试地图瓦片生成器
为QMapApp生成简单的测试瓦片
"""

import os
import math
from PIL import Image, ImageDraw, ImageFont

def deg2num(lat_deg, lon_deg, zoom):
    """将经纬度转换为瓦片坐标"""
    lat_rad = math.radians(lat_deg)
    n = 2.0 ** zoom
    x = int((lon_deg + 180.0) / 360.0 * n)
    y = int((1.0 - math.asinh(math.tan(lat_rad)) / math.pi) / 2.0 * n)
    return (x, y)

def num2deg(x, y, zoom):
    """将瓦片坐标转换为经纬度"""
    n = 2.0 ** zoom
    lon_deg = x / n * 360.0 - 180.0
    lat_rad = math.atan(math.sinh(math.pi * (1 - 2 * y / n)))
    lat_deg = math.degrees(lat_rad)
    return (lat_deg, lon_deg)

def create_tile(x, y, zoom, tile_size=256):
    """创建一个测试瓦片"""
    # 创建图像
    img = Image.new('RGB', (tile_size, tile_size), color='lightblue')
    draw = ImageDraw.Draw(img)
    
    # 绘制网格
    grid_size = 32
    for i in range(0, tile_size, grid_size):
        draw.line([(i, 0), (i, tile_size)], fill='white', width=1)
        draw.line([(0, i), (tile_size, i)], fill='white', width=1)
    
    # 绘制瓦片信息
    try:
        font = ImageFont.truetype("arial.ttf", 16)
    except:
        font = ImageFont.load_default()
    
    # 瓦片坐标
    text = f"Z:{zoom} X:{x} Y:{y}"
    bbox = draw.textbbox((0, 0), text, font=font)
    text_width = bbox[2] - bbox[0]
    text_height = bbox[3] - bbox[1]
    
    # 绘制背景矩形
    text_x = (tile_size - text_width) // 2
    text_y = tile_size // 2 - 30
    draw.rectangle([text_x-5, text_y-5, text_x+text_width+5, text_y+text_height+5], 
                   fill='white', outline='black')
    draw.text((text_x, text_y), text, fill='black', font=font)
    
    # 绘制经纬度信息
    lat, lon = num2deg(x, y, zoom)
    coord_text = f"({lat:.4f}, {lon:.4f})"
    bbox = draw.textbbox((0, 0), coord_text, font=font)
    coord_width = bbox[2] - bbox[0]
    coord_height = bbox[3] - bbox[1]
    
    coord_x = (tile_size - coord_width) // 2
    coord_y = tile_size // 2 + 10
    draw.rectangle([coord_x-5, coord_y-5, coord_x+coord_width+5, coord_y+coord_height+5], 
                   fill='white', outline='black')
    draw.text((coord_x, coord_y), coord_text, fill='blue', font=font)
    
    # 绘制边框
    draw.rectangle([0, 0, tile_size-1, tile_size-1], outline='red', width=2)
    
    return img

def generate_tiles_for_area(center_lat, center_lon, zoom_levels, radius_tiles=2, output_dir="map_tiles"):
    """为指定区域生成瓦片"""
    print(f"生成测试瓦片到目录: {output_dir}")
    print(f"中心点: ({center_lat}, {center_lon})")
    print(f"缩放级别: {zoom_levels}")
    print(f"半径: {radius_tiles} 瓦片")
    
    total_tiles = 0
    
    for zoom in zoom_levels:
        print(f"\n生成缩放级别 {zoom}...")
        
        # 计算中心瓦片坐标
        center_x, center_y = deg2num(center_lat, center_lon, zoom)
        
        # 创建目录
        zoom_dir = os.path.join(output_dir, str(zoom))
        os.makedirs(zoom_dir, exist_ok=True)
        
        tiles_count = 0
        
        # 生成周围的瓦片
        for x in range(center_x - radius_tiles, center_x + radius_tiles + 1):
            for y in range(center_y - radius_tiles, center_y + radius_tiles + 1):
                # 确保瓦片坐标有效
                max_tile = 2 ** zoom
                if x < 0 or x >= max_tile or y < 0 or y >= max_tile:
                    continue
                
                # 创建x目录
                x_dir = os.path.join(zoom_dir, str(x))
                os.makedirs(x_dir, exist_ok=True)
                
                # 生成瓦片
                tile = create_tile(x, y, zoom)
                tile_path = os.path.join(x_dir, f"{y}.png")
                tile.save(tile_path)
                
                tiles_count += 1
                total_tiles += 1
        
        print(f"缩放级别 {zoom} 完成，生成 {tiles_count} 个瓦片")
    
    print(f"\n总共生成 {total_tiles} 个瓦片")
    return total_tiles

def main():
    # 北京坐标
    beijing_lat = 39.9042
    beijing_lon = 116.4074
    
    # 生成多个缩放级别的瓦片
    zoom_levels = [8, 9, 10, 11, 12, 13, 14, 15]
    
    # 为北京地区生成测试瓦片
    generate_tiles_for_area(
        center_lat=beijing_lat,
        center_lon=beijing_lon,
        zoom_levels=zoom_levels,
        radius_tiles=3,  # 每个方向3个瓦片，总共7x7=49个瓦片每个缩放级别
        output_dir="map_tiles"
    )
    
    print("\n瓦片生成完成！")
    print("现在可以运行QMapApp来查看地图了。")

if __name__ == "__main__":
    main()
