#ifndef MESSAGEPARSER_H
#define MESSAGEPARSER_H

#include "IMessageParser.h"
#include <QJsonDocument>
#include <QJsonParseError>

/**
 * @brief JSON消息解析器
 * 
 * 实现JSON格式的消息解析
 */
class JsonMessageParser : public IMessageParser
{
public:
    JsonMessageParser();
    ~JsonMessageParser() override = default;
    
    QJsonObject parseMessage(const QByteArray& data) override;
    bool isValidMessage(const QByteArray& data) override;
    QString parserName() const override;
    QStringList supportedTypes() const override;

private:
    bool validateTargetMessage(const QJsonObject& message) const;
    QJsonObject normalizeMessage(const QJsonObject& message) const;
};

/**
 * @brief 二进制消息解析器
 * 
 * 实现自定义二进制格式的消息解析
 */
class BinaryMessageParser : public IMessageParser
{
public:
    BinaryMessageParser();
    ~BinaryMessageParser() override = default;
    
    QJsonObject parseMessage(const QByteArray& data) override;
    bool isValidMessage(const QByteArray& data) override;
    QString parserName() const override;
    QStringList supportedTypes() const override;

private:
    // 二进制消息头结构
    struct MessageHeader {
        quint32 magic;      // 魔数
        quint16 version;    // 版本号
        quint16 type;       // 消息类型
        quint32 length;     // 数据长度
    };
    
    static const quint32 MAGIC_NUMBER = 0x4D415054; // "MAPT"
    static const quint16 CURRENT_VERSION = 1;
    
    enum MessageType {
        TARGET_UPDATE = 1,
        TARGET_REMOVE = 2,
        HEARTBEAT = 3
    };
    
    bool validateHeader(const MessageHeader& header, int dataSize) const;
    QJsonObject parseTargetUpdate(const QByteArray& data) const;
    QJsonObject parseTargetRemove(const QByteArray& data) const;
    QJsonObject parseHeartbeat(const QByteArray& data) const;
};

#endif // MESSAGEPARSER_H
