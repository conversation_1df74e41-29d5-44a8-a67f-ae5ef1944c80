#include "CoordinateConverter.h"
#include "common/Constants.h"
#include <QtMath>
#include <QRect>

// 常量定义
const double CoordinateConverter::EARTH_RADIUS = 6378137.0;  // WGS84椭球半径(米)
const double CoordinateConverter::DEG_TO_RAD = M_PI / 180.0;
const double CoordinateConverter::RAD_TO_DEG = 180.0 / M_PI;

CoordinateConverter::CoordinateConverter()
{
}

QPointF CoordinateConverter::lonLatToTile(double longitude, double latitude, int zoom)
{
    if (!isValidLongitude(longitude) || !isValidLatitude(latitude) || !isValidZoomLevel(zoom)) {
        return QPointF();
    }
    
    double x = (longitude + 180.0) / 360.0 * (1 << zoom);
    double latRad = latitude * DEG_TO_RAD;
    double y = (1.0 - asinh(qTan(latRad)) / M_PI) / 2.0 * (1 << zoom);
    
    return QPointF(x, y);
}

QPointF CoordinateConverter::tileToLonLat(double tileX, double tileY, int zoom)
{
    if (!isValidZoomLevel(zoom)) {
        return QPointF();
    }
    
    double n = 1 << zoom;
    double longitude = tileX / n * 360.0 - 180.0;
    double latRad = qAtan(sinh(M_PI * (1 - 2 * tileY / n)));
    double latitude = latRad * RAD_TO_DEG;
    
    return QPointF(longitude, latitude);
}

QPointF CoordinateConverter::tileToScene(double tileX, double tileY, int zoom)
{
    if (!isValidZoomLevel(zoom)) {
        return QPointF();
    }
    
    // 计算场景坐标原点偏移
    double offset = (1 << (zoom - 1)) * Constants::TILE_SIZE;
    
    double sceneX = tileX * Constants::TILE_SIZE - offset;
    double sceneY = tileY * Constants::TILE_SIZE - offset;
    
    return QPointF(sceneX, sceneY);
}

QPointF CoordinateConverter::sceneToTile(double sceneX, double sceneY, int zoom)
{
    if (!isValidZoomLevel(zoom)) {
        return QPointF();
    }
    
    // 计算场景坐标原点偏移
    double offset = (1 << (zoom - 1)) * Constants::TILE_SIZE;
    
    double tileX = (sceneX + offset) / Constants::TILE_SIZE;
    double tileY = (sceneY + offset) / Constants::TILE_SIZE;
    
    return QPointF(tileX, tileY);
}

QPointF CoordinateConverter::lonLatToScene(double longitude, double latitude, int zoom)
{
    QPointF tilePos = lonLatToTile(longitude, latitude, zoom);
    if (tilePos.isNull()) {
        return QPointF();
    }
    
    return tileToScene(tilePos.x(), tilePos.y(), zoom);
}

QPointF CoordinateConverter::sceneToLonLat(double sceneX, double sceneY, int zoom)
{
    QPointF tilePos = sceneToTile(sceneX, sceneY, zoom);
    if (tilePos.isNull()) {
        return QPointF();
    }
    
    return tileToLonLat(tilePos.x(), tilePos.y(), zoom);
}

QRect CoordinateConverter::calculateTileRect(const QRectF& sceneRect, int zoom)
{
    if (!isValidZoomLevel(zoom) || sceneRect.isEmpty()) {
        return QRect();
    }
    
    // 转换场景矩形的四个角点到瓦片坐标
    QPointF topLeft = sceneToTile(sceneRect.left(), sceneRect.top(), zoom);
    QPointF bottomRight = sceneToTile(sceneRect.right(), sceneRect.bottom(), zoom);
    
    // 计算瓦片范围，向外扩展一个瓦片以确保覆盖
    int minX = qMax(0, static_cast<int>(qFloor(topLeft.x())) - 1);
    int minY = qMax(0, static_cast<int>(qFloor(topLeft.y())) - 1);
    int maxX = qMin((1 << zoom) - 1, static_cast<int>(qCeil(bottomRight.x())) + 1);
    int maxY = qMin((1 << zoom) - 1, static_cast<int>(qCeil(bottomRight.y())) + 1);
    
    return QRect(minX, minY, maxX - minX + 1, maxY - minY + 1);
}

double CoordinateConverter::distanceMeters(double lat1, double lon1, double lat2, double lon2)
{
    if (!isValidLatitude(lat1) || !isValidLongitude(lon1) ||
        !isValidLatitude(lat2) || !isValidLongitude(lon2)) {
        return 0.0;
    }
    
    // 使用Haversine公式计算球面距离
    double dLat = (lat2 - lat1) * DEG_TO_RAD;
    double dLon = (lon2 - lon1) * DEG_TO_RAD;
    
    double a = qSin(dLat / 2) * qSin(dLat / 2) +
               qCos(lat1 * DEG_TO_RAD) * qCos(lat2 * DEG_TO_RAD) *
               qSin(dLon / 2) * qSin(dLon / 2);
    
    double c = 2 * qAtan2(qSqrt(a), qSqrt(1 - a));
    
    return EARTH_RADIUS * c;
}

double CoordinateConverter::bearing(double lat1, double lon1, double lat2, double lon2)
{
    if (!isValidLatitude(lat1) || !isValidLongitude(lon1) ||
        !isValidLatitude(lat2) || !isValidLongitude(lon2)) {
        return 0.0;
    }
    
    double dLon = (lon2 - lon1) * DEG_TO_RAD;
    double lat1Rad = lat1 * DEG_TO_RAD;
    double lat2Rad = lat2 * DEG_TO_RAD;
    
    double y = qSin(dLon) * qCos(lat2Rad);
    double x = qCos(lat1Rad) * qSin(lat2Rad) - qSin(lat1Rad) * qCos(lat2Rad) * qCos(dLon);
    
    double bearingRad = qAtan2(y, x);
    double bearingDeg = bearingRad * RAD_TO_DEG;
    
    // 转换为0-360度范围
    return fmod(bearingDeg + 360.0, 360.0);
}

bool CoordinateConverter::isValidLatitude(double latitude)
{
    return latitude >= -90.0 && latitude <= 90.0;
}

bool CoordinateConverter::isValidLongitude(double longitude)
{
    return longitude >= -180.0 && longitude <= 180.0;
}

bool CoordinateConverter::isValidZoomLevel(int zoom)
{
    return zoom >= Constants::MIN_ZOOM_LEVEL && zoom <= Constants::MAX_ZOOM_LEVEL;
}

// 辅助数学函数
double CoordinateConverter::sinh(double x)
{
    return (qExp(x) - qExp(-x)) / 2.0;
}

double CoordinateConverter::asinh(double x)
{
    return qLn(x + qSqrt(x * x + 1.0));
}

double CoordinateConverter::atanh(double x)
{
    return 0.5 * qLn((1.0 + x) / (1.0 - x));
}
