#ifndef CONSTANTS_H
#define CONSTANTS_H

#include <QString>

namespace Constants {
    // 地图相关常量
    const int TILE_SIZE = 256;                    // 瓦片大小(像素)
    const int MIN_ZOOM_LEVEL = 1;                 // 最小缩放级别
    const int MAX_ZOOM_LEVEL = 18;                // 最大缩放级别
    const int DEFAULT_ZOOM_LEVEL = 10;            // 默认缩放级别
    
    // 缓存相关常量
    const int DEFAULT_CACHE_SIZE_MB = 500;        // 默认缓存大小(MB)
    const int MAX_CACHE_ITEMS = 10000;            // 最大缓存项数
    
    // 网络相关常量
    const int UDP_BUFFER_SIZE = 65536;            // UDP缓冲区大小
    const int DEFAULT_UDP_PORT = 15000;           // 默认UDP端口
    const QString DEFAULT_MULTICAST_GROUP = "239.255.10.1";
    
    // 目标相关常量
    const int TARGET_ICON_SIZE = 32;              // 目标图标大小
    const int ANIMATION_DURATION = 1000;         // 动画持续时间(毫秒)
    const double TARGET_UPDATE_THRESHOLD = 0.0001; // 目标更新阈值(度)
    
    // 文件路径常量
    const QString CONFIG_FILE = "appconfig.json";
    const QString DEFAULT_TILE_PATH = "./map_tiles";
    const QString CACHE_DIR = "./cache";
    const QString LOG_DIR = "./logs";
    
    // 应用信息
    const QString APP_NAME = "QMapApp";
    const QString APP_ORGANIZATION = "MapSoft";
    const QString APP_DOMAIN = "mapsoft.com";
}

#endif // CONSTANTS_H
