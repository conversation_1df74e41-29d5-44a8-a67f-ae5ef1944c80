#ifndef TILELOADER_H
#define TILELOADER_H

#include <QObject>
#include <QThreadPool>
#include <QRunnable>
#include <QPixmap>
#include <QRect>
#include <QSet>
#include <QMutex>

class TileCache;

/**
 * @brief 瓦片加载任务
 */
class TileLoadTask : public QObject, public QRunnable
{
    Q_OBJECT

public:
    TileLoadTask(int x, int y, int z, const QString& tilePath);
    void run() override;

signals:
    void tileLoaded(int x, int y, int z, const QPixmap& pixmap);
    void tileLoadFailed(int x, int y, int z);

private:
    int m_x, m_y, m_z;
    QString m_tilePath;
};

/**
 * @brief 瓦片加载器
 * 
 * 负责异步加载地图瓦片，支持缓存和优先级管理
 */
class TileLoader : public QObject
{
    Q_OBJECT

public:
    explicit TileLoader(QObject *parent = nullptr);
    ~TileLoader();
    
    // 设置瓦片源路径
    void setTileSource(const QString& tilePath);
    
    // 加载指定区域的瓦片
    void loadTiles(const QRect& tileRect, int zoomLevel);
    
    // 清除加载队列
    void clearQueue();
    
    // 缓存管理
    void setCacheSize(int sizeMB);
    void clearCache();
    
    // 获取瓦片（如果已缓存）
    QPixmap getTile(int x, int y, int z);
    bool hasTile(int x, int y, int z);

signals:
    void tileLoaded(int x, int y, int z, const QPixmap& pixmap);
    void loadingProgress(int loaded, int total);

private slots:
    void onTileLoaded(int x, int y, int z, const QPixmap& pixmap);
    void onTileLoadFailed(int x, int y, int z);

private:
    QString getTilePath(int x, int y, int z) const;
    QString tileKey(int x, int y, int z) const;
    void updateProgress();

private:
    QThreadPool* m_threadPool;
    TileCache* m_cache;
    QString m_tileSource;
    
    // 加载状态跟踪
    QSet<QString> m_loadingTiles;
    QSet<QString> m_requestedTiles;
    QMutex m_mutex;
    
    int m_totalRequested;
    int m_totalLoaded;
};

#endif // TILELOADER_H
