#include <QtTest>
#include <QObject>
#include "../src/utils/CoordinateConverter.h"

class TestCoordinateConverter : public QObject
{
    Q_OBJECT

private slots:
    void testLonLatToTile();
    void testTileToLonLat();
    void testTileToScene();
    void testSceneToTile();
    void testLonLatToScene();
    void testSceneToLonLat();
    void testDistanceCalculation();
    void testBearingCalculation();
    void testValidation();
    void testTileRectCalculation();

private:
    const double EPSILON = 1e-6;
    
    bool isEqual(double a, double b, double epsilon = 1e-6) {
        return qAbs(a - b) < epsilon;
    }
};

void TestCoordinateConverter::testLonLatToTile()
{
    // 测试北京坐标转换为瓦片坐标
    double lat = 39.9042;
    double lon = 116.4074;
    int zoom = 10;
    
    QPointF tilePos = CoordinateConverter::lonLatToTile(lon, lat, zoom);
    
    // 验证结果在合理范围内
    QVERIFY(tilePos.x() >= 0 && tilePos.x() < (1 << zoom));
    QVERIFY(tilePos.y() >= 0 && tilePos.y() < (1 << zoom));
    
    // 测试边界情况
    QPointF northPole = CoordinateConverter::lonLatToTile(0, 85, zoom);
    QPointF southPole = CoordinateConverter::lonLatToTile(0, -85, zoom);
    
    QVERIFY(northPole.y() < southPole.y()); // 北极的Y坐标应该小于南极
}

void TestCoordinateConverter::testTileToLonLat()
{
    // 测试瓦片坐标转换为经纬度
    int zoom = 10;
    double tileX = 512.5;
    double tileY = 256.3;
    
    QPointF lonLat = CoordinateConverter::tileToLonLat(tileX, tileY, zoom);
    
    // 验证结果在有效范围内
    QVERIFY(lonLat.x() >= -180.0 && lonLat.x() <= 180.0); // 经度
    QVERIFY(lonLat.y() >= -85.0 && lonLat.y() <= 85.0);   // 纬度
}

void TestCoordinateConverter::testTileToScene()
{
    int zoom = 10;
    double tileX = 100;
    double tileY = 200;
    
    QPointF scenePos = CoordinateConverter::tileToScene(tileX, tileY, zoom);
    
    // 验证场景坐标计算
    double expectedX = tileX * 256 - (1 << (zoom - 1)) * 256;
    double expectedY = tileY * 256 - (1 << (zoom - 1)) * 256;
    
    QVERIFY(isEqual(scenePos.x(), expectedX));
    QVERIFY(isEqual(scenePos.y(), expectedY));
}

void TestCoordinateConverter::testSceneToTile()
{
    int zoom = 10;
    double sceneX = 1000;
    double sceneY = 2000;
    
    QPointF tilePos = CoordinateConverter::sceneToTile(sceneX, sceneY, zoom);
    
    // 验证逆转换
    QPointF backToScene = CoordinateConverter::tileToScene(tilePos.x(), tilePos.y(), zoom);
    
    QVERIFY(isEqual(backToScene.x(), sceneX, 1.0));
    QVERIFY(isEqual(backToScene.y(), sceneY, 1.0));
}

void TestCoordinateConverter::testLonLatToScene()
{
    double lat = 39.9042;
    double lon = 116.4074;
    int zoom = 10;
    
    QPointF scenePos = CoordinateConverter::lonLatToScene(lon, lat, zoom);
    
    // 验证组合转换
    QPointF tilePos = CoordinateConverter::lonLatToTile(lon, lat, zoom);
    QPointF expectedScene = CoordinateConverter::tileToScene(tilePos.x(), tilePos.y(), zoom);
    
    QVERIFY(isEqual(scenePos.x(), expectedScene.x()));
    QVERIFY(isEqual(scenePos.y(), expectedScene.y()));
}

void TestCoordinateConverter::testSceneToLonLat()
{
    double sceneX = 1000;
    double sceneY = 2000;
    int zoom = 10;
    
    QPointF lonLat = CoordinateConverter::sceneToLonLat(sceneX, sceneY, zoom);
    
    // 验证逆转换
    QPointF backToScene = CoordinateConverter::lonLatToScene(lonLat.x(), lonLat.y(), zoom);
    
    QVERIFY(isEqual(backToScene.x(), sceneX, 1.0));
    QVERIFY(isEqual(backToScene.y(), sceneY, 1.0));
}

void TestCoordinateConverter::testDistanceCalculation()
{
    // 测试北京到上海的距离
    double beijingLat = 39.9042, beijingLon = 116.4074;
    double shanghaiLat = 31.2304, shanghaiLon = 121.4737;
    
    double distance = CoordinateConverter::distanceMeters(
        beijingLat, beijingLon, shanghaiLat, shanghaiLon);
    
    // 北京到上海大约1000公里
    QVERIFY(distance > 900000 && distance < 1200000);
    
    // 测试同一点的距离
    double samePointDistance = CoordinateConverter::distanceMeters(
        beijingLat, beijingLon, beijingLat, beijingLon);
    
    QVERIFY(isEqual(samePointDistance, 0.0, 1.0));
}

void TestCoordinateConverter::testBearingCalculation()
{
    // 测试正北方向
    double bearing = CoordinateConverter::bearing(0, 0, 1, 0);
    QVERIFY(isEqual(bearing, 0.0, 1.0)); // 正北
    
    // 测试正东方向
    bearing = CoordinateConverter::bearing(0, 0, 0, 1);
    QVERIFY(isEqual(bearing, 90.0, 1.0)); // 正东
    
    // 测试正南方向
    bearing = CoordinateConverter::bearing(0, 0, -1, 0);
    QVERIFY(isEqual(bearing, 180.0, 1.0)); // 正南
    
    // 测试正西方向
    bearing = CoordinateConverter::bearing(0, 0, 0, -1);
    QVERIFY(isEqual(bearing, 270.0, 1.0)); // 正西
}

void TestCoordinateConverter::testValidation()
{
    // 测试有效坐标
    QVERIFY(CoordinateConverter::isValidLatitude(39.9042));
    QVERIFY(CoordinateConverter::isValidLongitude(116.4074));
    QVERIFY(CoordinateConverter::isValidZoomLevel(10));
    
    // 测试无效坐标
    QVERIFY(!CoordinateConverter::isValidLatitude(91.0));
    QVERIFY(!CoordinateConverter::isValidLatitude(-91.0));
    QVERIFY(!CoordinateConverter::isValidLongitude(181.0));
    QVERIFY(!CoordinateConverter::isValidLongitude(-181.0));
    QVERIFY(!CoordinateConverter::isValidZoomLevel(0));
    QVERIFY(!CoordinateConverter::isValidZoomLevel(19));
}

void TestCoordinateConverter::testTileRectCalculation()
{
    QRectF sceneRect(0, 0, 1000, 1000);
    int zoom = 10;
    
    QRect tileRect = CoordinateConverter::calculateTileRect(sceneRect, zoom);
    
    // 验证瓦片矩形不为空
    QVERIFY(!tileRect.isEmpty());
    QVERIFY(tileRect.width() > 0);
    QVERIFY(tileRect.height() > 0);
    
    // 验证瓦片坐标在有效范围内
    int maxTile = (1 << zoom) - 1;
    QVERIFY(tileRect.left() >= 0);
    QVERIFY(tileRect.top() >= 0);
    QVERIFY(tileRect.right() <= maxTile);
    QVERIFY(tileRect.bottom() <= maxTile);
}

QTEST_MAIN(TestCoordinateConverter)
#include "test_coordinate_converter.moc"
