# QMapApp - Qt离线地图应用程序

基于Qt 5.14的高性能桌面地图应用程序，支持离线地图瓦片显示和UDP组播目标数据接收。

## 功能特性

- **离线地图显示**: 支持标准XYZ瓦片格式，流畅的缩放和拖拽操作
- **UDP组播接收**: 多组播组支持，实时接收目标位置数据
- **目标管理**: 实时显示和管理地图上的目标对象
- **高性能渲染**: 基于QGraphicsView的硬件加速渲染
- **模块化架构**: 清晰的分层架构，易于扩展和维护

## 系统要求

- Qt 5.14 或更高版本
- C++17 编译器支持
- Windows/Linux/macOS

## 项目结构

```
QMapApp/
├── src/                          # 源代码目录
│   ├── common/                   # 公共定义
│   │   ├── Constants.h           # 常量定义
│   │   └── Target.h              # 目标数据结构
│   ├── main.cpp                  # 程序入口
│   ├── MainWindow.h/.cpp         # 主窗口
│   ├── map/                      # 地图模块
│   │   ├── MapWidget.h/.cpp      # 地图显示组件
│   │   ├── TileLoader.h/.cpp     # 瓦片加载器
│   │   └── TileCache.h/.cpp      # 瓦片缓存
│   ├── network/                  # 网络模块
│   │   ├── UdpReceiver.h/.cpp    # UDP接收器
│   │   ├── IMessageParser.h      # 消息解析接口
│   │   └── MessageParser.h/.cpp  # 消息解析实现
│   ├── managers/                 # 管理器模块
│   │   └── TargetManager.h/.cpp  # 目标管理器
│   ├── models/                   # 数据模型
│   │   └── TargetModel.h/.cpp    # 目标数据模型
│   ├── graphics/                 # 图形组件
│   │   └── TargetItem.h/.cpp     # 目标图形项
│   └── utils/                    # 工具类
│       ├── ConfigManager.h/.cpp  # 配置管理器
│       └── CoordinateConverter.h/.cpp # 坐标转换
├── resources/                    # 资源文件
│   ├── resources.qrc             # 资源配置
│   ├── icons/                    # 图标文件
│   └── config/                   # 配置文件
├── map_tiles/                    # 地图瓦片目录
├── cache/                        # 缓存目录
├── logs/                         # 日志目录
├── appconfig.json                # 应用配置文件
├── QMapApp.pro                   # QMake工程文件
└── README.md                     # 项目说明
```

## 编译和运行

### 快速开始

#### Windows系统
```cmd
# 运行构建脚本
build.bat

# 或手动编译
mkdir build
cd build
qmake ..\QMapApp.pro
nmake
# 或使用 mingw32-make (如果使用MinGW)
```

#### Linux/macOS系统
```bash
# 运行构建脚本
./build.sh

# 或手动编译
mkdir build
cd build
qmake ../QMapApp.pro
make
```

### 使用Qt Creator

1. 打开 `QMapApp.pro` 文件
2. 配置编译器和Qt版本（推荐Qt 5.14+）
3. 点击"构建"按钮编译项目
4. 点击"运行"按钮启动应用程序

### 生成测试数据

#### 生成测试地图瓦片
```bash
# 需要Python和PIL库
pip install Pillow
python generate_test_tiles.py
```

#### 测试UDP功能
```bash
# 在另一个终端运行UDP测试发送器
python test_udp_sender.py

# 可选参数
python test_udp_sender.py --group ************ --port 15000 --targets 5 --duration 60
```

## 配置说明

应用程序使用JSON格式的配置文件 `appconfig.json`：

```json
{
    "network": {
        "multicast_groups": [
            {
                "address": "************",
                "port": 15000,
                "enabled": true,
                "name": "Primary Group"
            }
        ],
        "message_parser": "json"
    },
    "map": {
        "tile_source": "./map_tiles",
        "cache_size_mb": 500,
        "initial_center": {
            "lat": 39.9042,
            "lon": 116.4074
        },
        "initial_zoom_level": 10
    },
    "targets": {
        "default_icon": ":/icons/target.png",
        "animation_duration": 1000
    }
}
```

## 地图瓦片

应用程序支持标准的XYZ瓦片格式，瓦片文件应按以下结构组织：

```
map_tiles/
├── 1/
│   ├── 0/
│   │   └── 0.png
│   └── 1/
│       ├── 0.png
│       └── 1.png
├── 2/
│   └── ...
└── ...
```

## UDP消息格式

应用程序支持JSON格式的目标数据消息：

```json
{
    "id": "target_001",
    "name": "目标1",
    "latitude": 39.9042,
    "longitude": 116.4074,
    "altitude": 100.0,
    "heading": 45.0,
    "speed": 10.5,
    "type": "vehicle",
    "status": "active"
}
```

## 开发状态

- [x] 基础架构和模块框架
- [x] 地图瓦片加载和显示功能
- [x] 坐标转换系统
- [x] UDP网络通信模块
- [x] 目标管理和可视化
- [x] 配置管理和异常处理
- [ ] 性能优化和内存管理
- [ ] 单元测试和集成测试

### 已实现功能

✅ **核心架构**
- 模块化分层架构设计
- QMake工程配置
- 资源管理系统

✅ **地图显示**
- 离线瓦片地图显示
- 流畅的缩放和拖拽
- 坐标系统转换
- 瓦片缓存管理

✅ **网络通信**
- UDP组播接收
- JSON/二进制消息解析
- 多组播组支持
- 网络状态监控

✅ **目标管理**
- 实时目标显示
- 目标数据模型
- 动画效果
- 目标列表视图

✅ **配置管理**
- JSON配置文件
- 运行时配置更新
- 默认配置生成

### 待完成功能

🔄 **性能优化**
- 内存使用优化
- 渲染性能提升
- 大量目标处理

🔄 **测试覆盖**
- 单元测试
- 集成测试
- 性能测试

## 许可证

本项目采用MIT许可证，详见LICENSE文件。

## 贡献

欢迎提交Issue和Pull Request来改进项目。
