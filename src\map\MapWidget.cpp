#include "MapWidget.h"
#include "TileLoader.h"
#include "utils/CoordinateConverter.h"
#include "graphics/TargetItem.h"
#include "common/Constants.h"
#include "utils/ConfigManager.h"

#include <QGraphicsPixmapItem>
#include <QScrollBar>
#include <QApplication>
#include <QDebug>
#include <QtMath>

// 常量定义
const double MapWidget::ZOOM_FACTOR = 1.2;
const int MapWidget::UPDATE_DELAY = 100; // 毫秒

MapWidget::MapWidget(QWidget *parent)
    : QGraphicsView(parent)
    , m_scene(nullptr)
    , m_tileLoader(nullptr)
    , m_converter(new CoordinateConverter())
    , m_zoomLevel(Constants::DEFAULT_ZOOM_LEVEL)
    , m_center(116.4074, 39.9042)  // 默认北京坐标
    , m_dragging(false)
    , m_updateTimer(new QTimer(this))
{
    setupScene();
    setupTileLoader();
    
    // 设置视图属性
    setDragMode(QGraphicsView::NoDrag);
    setHorizontalScrollBarPolicy(Qt::ScrollBarAlwaysOff);
    setVerticalScrollBarPolicy(Qt::ScrollBarAlwaysOff);
    setRenderHint(QPainter::Antialiasing, true);
    setRenderHint(QPainter::SmoothPixmapTransform, true);
    setOptimizationFlag(QGraphicsView::DontAdjustForAntialiasing, true);
    setOptimizationFlag(QGraphicsView::DontSavePainterState, true);
    setViewportUpdateMode(QGraphicsView::MinimalViewportUpdate);
    
    // 设置更新定时器
    m_updateTimer->setSingleShot(true);
    m_updateTimer->setInterval(UPDATE_DELAY);
    connect(m_updateTimer, &QTimer::timeout, this, &MapWidget::updateVisibleTiles);
    
    // 从配置加载初始设置
    ConfigManager& config = ConfigManager::instance();
    QPointF initialCenter = config.initialCenter();
    m_center = QPointF(initialCenter.x(), initialCenter.y());
    m_zoomLevel = config.initialZoomLevel();
    
    // 设置初始视图
    resetView();
    
    qDebug() << "MapWidget initialized at zoom level" << m_zoomLevel 
             << "center" << m_center;
}

MapWidget::~MapWidget()
{
    delete m_converter;
}

void MapWidget::setupScene()
{
    m_scene = new QGraphicsScene(this);
    
    // 设置场景大小为足够大的区域
    const double sceneSize = (1 << Constants::MAX_ZOOM_LEVEL) * Constants::TILE_SIZE;
    m_scene->setSceneRect(-sceneSize/2, -sceneSize/2, sceneSize, sceneSize);
    
    setScene(m_scene);
}

void MapWidget::setupTileLoader()
{
    m_tileLoader = new TileLoader(this);
    
    // 连接瓦片加载信号
    connect(m_tileLoader, &TileLoader::tileLoaded, 
            this, &MapWidget::onTileLoaded);
    connect(m_tileLoader, &TileLoader::loadingProgress, 
            this, &MapWidget::onLoadingProgress);
    
    // 设置瓦片源
    ConfigManager& config = ConfigManager::instance();
    m_tileLoader->setTileSource(config.tileSource());
    m_tileLoader->setCacheSize(config.cacheSizeMB());
}

void MapWidget::setCenter(double latitude, double longitude)
{
    if (!CoordinateConverter::isValidLatitude(latitude) || 
        !CoordinateConverter::isValidLongitude(longitude)) {
        return;
    }
    
    m_center = QPointF(longitude, latitude);
    
    // 转换为场景坐标并居中视图
    QPointF scenePos = CoordinateConverter::lonLatToScene(longitude, latitude, m_zoomLevel);
    centerOn(scenePos);
    
    emit centerChanged(latitude, longitude);
    
    // 延迟更新瓦片
    m_updateTimer->start();
}

void MapWidget::setZoomLevel(int level)
{
    if (!CoordinateConverter::isValidZoomLevel(level) || level == m_zoomLevel) {
        return;
    }
    
    int oldZoom = m_zoomLevel;
    m_zoomLevel = level;
    
    // 计算缩放因子
    double scaleFactor = qPow(2.0, m_zoomLevel - oldZoom);
    
    // 应用缩放变换
    QTransform transform = this->transform();
    transform.scale(scaleFactor, scaleFactor);
    setTransform(transform);
    
    emit zoomLevelChanged(m_zoomLevel);
    
    // 清除旧的瓦片并加载新的
    m_scene->clear();
    m_updateTimer->start();
}

void MapWidget::resetView()
{
    // 重置变换
    resetTransform();

    // 设置中心点
    setCenter(m_center.y(), m_center.x());

    qDebug() << "View reset to center" << m_center << "zoom" << m_zoomLevel;
}

void MapWidget::addTarget(const QString& targetId, double latitude, double longitude)
{
    if (m_targets.contains(targetId)) {
        updateTarget(targetId, latitude, longitude);
        return;
    }

    Target target(targetId, latitude, longitude);
    TargetItem* item = new TargetItem(target);

    // 转换为场景坐标
    QPointF scenePos = CoordinateConverter::lonLatToScene(longitude, latitude, m_zoomLevel);
    item->setPos(scenePos);

    // 连接信号
    connect(item, &TargetItem::clicked, this, &MapWidget::targetClicked);

    // 添加到场景和映射表
    m_scene->addItem(item);
    m_targets[targetId] = item;

    qDebug() << "Target added:" << targetId << "at" << latitude << longitude;
}

void MapWidget::updateTarget(const QString& targetId, double latitude, double longitude)
{
    TargetItem* item = m_targets.value(targetId);
    if (!item) {
        addTarget(targetId, latitude, longitude);
        return;
    }

    // 更新目标数据
    Target target = item->target();
    target.setPosition(latitude, longitude);
    item->updateTarget(target);

    // 转换为场景坐标并动画移动
    QPointF scenePos = CoordinateConverter::lonLatToScene(longitude, latitude, m_zoomLevel);
    item->animateToPosition(scenePos);

    qDebug() << "Target updated:" << targetId << "to" << latitude << longitude;
}

void MapWidget::removeTarget(const QString& targetId)
{
    TargetItem* item = m_targets.take(targetId);
    if (item) {
        m_scene->removeItem(item);
        delete item;
        qDebug() << "Target removed:" << targetId;
    }
}

QPointF MapWidget::getCenter() const
{
    return m_center;
}

int MapWidget::getZoomLevel() const
{
    return m_zoomLevel;
}

void MapWidget::wheelEvent(QWheelEvent *event)
{
    // 计算缩放中心点
    QPointF scenePos = mapToScene(event->pos());

    // 计算缩放因子
    double factor = event->angleDelta().y() > 0 ? ZOOM_FACTOR : 1.0 / ZOOM_FACTOR;

    // 执行平滑缩放
    smoothZoom(factor, scenePos);

    event->accept();
}

void MapWidget::mousePressEvent(QMouseEvent *event)
{
    if (event->button() == Qt::LeftButton) {
        m_dragging = true;
        m_lastPanPoint = event->pos();
        setCursor(Qt::ClosedHandCursor);
    }

    QGraphicsView::mousePressEvent(event);
}

void MapWidget::mouseMoveEvent(QMouseEvent *event)
{
    if (m_dragging && (event->buttons() & Qt::LeftButton)) {
        // 计算拖拽偏移
        QPoint delta = event->pos() - m_lastPanPoint;
        m_lastPanPoint = event->pos();

        // 移动视图
        QScrollBar* hBar = horizontalScrollBar();
        QScrollBar* vBar = verticalScrollBar();
        hBar->setValue(hBar->value() - delta.x());
        vBar->setValue(vBar->value() - delta.y());

        // 延迟更新瓦片
        m_updateTimer->start();
    }

    QGraphicsView::mouseMoveEvent(event);
}

void MapWidget::mouseReleaseEvent(QMouseEvent *event)
{
    if (event->button() == Qt::LeftButton) {
        m_dragging = false;
        setCursor(Qt::ArrowCursor);

        // 更新中心坐标
        QPointF sceneCenter = mapToScene(viewport()->rect().center());
        QPointF lonLat = CoordinateConverter::sceneToLonLat(sceneCenter.x(), sceneCenter.y(), m_zoomLevel);

        if (CoordinateConverter::isValidLongitude(lonLat.x()) &&
            CoordinateConverter::isValidLatitude(lonLat.y())) {
            m_center = lonLat;
            emit centerChanged(lonLat.y(), lonLat.x());
        }
    }

    QGraphicsView::mouseReleaseEvent(event);
}

void MapWidget::keyPressEvent(QKeyEvent *event)
{
    switch (event->key()) {
    case Qt::Key_Plus:
    case Qt::Key_Equal:
        setZoomLevel(m_zoomLevel + 1);
        break;
    case Qt::Key_Minus:
        setZoomLevel(m_zoomLevel - 1);
        break;
    case Qt::Key_Home:
        resetView();
        break;
    default:
        QGraphicsView::keyPressEvent(event);
    }
}

void MapWidget::resizeEvent(QResizeEvent *event)
{
    QGraphicsView::resizeEvent(event);

    // 视图大小改变时延迟更新瓦片
    m_updateTimer->start();
}

void MapWidget::onTileLoaded(int x, int y, int z, const QPixmap& pixmap)
{
    // 只处理当前缩放级别的瓦片
    if (z != m_zoomLevel) {
        return;
    }

    // 转换瓦片坐标到场景坐标
    QPointF scenePos = CoordinateConverter::tileToScene(x, y, z);

    // 创建瓦片图形项
    QGraphicsPixmapItem* tileItem = new QGraphicsPixmapItem(pixmap);
    tileItem->setPos(scenePos);
    tileItem->setZValue(-1); // 确保瓦片在目标下方

    // 添加到场景
    m_scene->addItem(tileItem);

    qDebug() << "Tile displayed:" << x << y << z << "at scene pos" << scenePos;
}

void MapWidget::onLoadingProgress(int loaded, int total)
{
    if (total > 0) {
        double progress = static_cast<double>(loaded) / total * 100.0;
        qDebug() << "Tile loading progress:" << progress << "%" << "(" << loaded << "/" << total << ")";
    }
}

void MapWidget::updateVisibleTiles()
{
    // 获取当前可见的场景矩形
    QRectF visibleRect = mapToScene(viewport()->rect()).boundingRect();

    // 计算需要加载的瓦片范围
    QRect tileRect = CoordinateConverter::calculateTileRect(visibleRect, m_zoomLevel);

    if (!tileRect.isEmpty()) {
        // 清除旧的瓦片（保留目标）
        QList<QGraphicsItem*> items = m_scene->items();
        for (QGraphicsItem* item : items) {
            QGraphicsPixmapItem* pixmapItem = qgraphicsitem_cast<QGraphicsPixmapItem*>(item);
            if (pixmapItem && !qgraphicsitem_cast<TargetItem*>(item)) {
                m_scene->removeItem(pixmapItem);
                delete pixmapItem;
            }
        }

        // 加载新的瓦片
        m_tileLoader->loadTiles(tileRect, m_zoomLevel);

        qDebug() << "Updating tiles for rect:" << tileRect << "zoom:" << m_zoomLevel;
    }
}

void MapWidget::constrainView()
{
    // 限制视图范围在有效的地理坐标内
    QPointF sceneCenter = mapToScene(viewport()->rect().center());
    QPointF lonLat = CoordinateConverter::sceneToLonLat(sceneCenter.x(), sceneCenter.y(), m_zoomLevel);

    bool needsConstraint = false;

    if (lonLat.x() < -180.0) {
        lonLat.setX(-180.0);
        needsConstraint = true;
    } else if (lonLat.x() > 180.0) {
        lonLat.setX(180.0);
        needsConstraint = true;
    }

    if (lonLat.y() < -85.0) {
        lonLat.setY(-85.0);
        needsConstraint = true;
    } else if (lonLat.y() > 85.0) {
        lonLat.setY(85.0);
        needsConstraint = true;
    }

    if (needsConstraint) {
        QPointF constrainedScene = CoordinateConverter::lonLatToScene(lonLat.x(), lonLat.y(), m_zoomLevel);
        centerOn(constrainedScene);
    }
}

void MapWidget::smoothZoom(double factor, const QPointF& center)
{
    // 计算新的缩放级别
    int newZoomLevel = m_zoomLevel;
    if (factor > 1.0 && m_zoomLevel < Constants::MAX_ZOOM_LEVEL) {
        newZoomLevel++;
    } else if (factor < 1.0 && m_zoomLevel > Constants::MIN_ZOOM_LEVEL) {
        newZoomLevel--;
    } else {
        return; // 无法缩放
    }

    // 计算缩放前的中心点经纬度
    QPointF oldCenter = CoordinateConverter::sceneToLonLat(center.x(), center.y(), m_zoomLevel);

    // 更新缩放级别
    setZoomLevel(newZoomLevel);

    // 重新计算缩放后的场景坐标并居中
    QPointF newCenter = CoordinateConverter::lonLatToScene(oldCenter.x(), oldCenter.y(), m_zoomLevel);
    centerOn(newCenter);

    // 约束视图
    constrainView();
}
