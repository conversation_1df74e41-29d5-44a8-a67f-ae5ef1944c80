#include "MessageParser.h"
#include <QJsonDocument>
#include <QJsonParseError>
#include <QJsonArray>
#include <QDebug>
#include <QDateTime>
#include <QDataStream>

// JsonMessageParser 实现
JsonMessageParser::JsonMessageParser()
{
}

QJsonObject JsonMessageParser::parseMessage(const QByteArray& data)
{
    if (!isValidMessage(data)) {
        return QJsonObject();
    }
    
    QJsonParseError error;
    QJsonDocument doc = QJsonDocument::fromJson(data, &error);
    
    if (error.error != QJsonParseError::NoError) {
        qWarning() << "JSON parse error:" << error.errorString();
        return QJsonObject();
    }
    
    QJsonObject message = doc.object();
    
    // 验证目标消息格式
    if (!validateTargetMessage(message)) {
        qWarning() << "Invalid target message format";
        return QJsonObject();
    }
    
    // 标准化消息格式
    return normalizeMessage(message);
}

bool JsonMessageParser::isValidMessage(const QByteArray& data)
{
    if (data.isEmpty() || data.size() > 65536) { // 64KB限制
        return false;
    }
    
    // 简单检查是否为JSON格式
    QByteArray trimmed = data.trimmed();
    return (trimmed.startsWith('{') && trimmed.endsWith('}')) ||
           (trimmed.startsWith('[') && trimmed.endsWith(']'));
}

QString JsonMessageParser::parserName() const
{
    return "JSON Parser";
}

QStringList JsonMessageParser::supportedTypes() const
{
    return QStringList() << "target_update" << "target_remove" << "heartbeat";
}

bool JsonMessageParser::validateTargetMessage(const QJsonObject& message) const
{
    // 检查必需字段
    if (!message.contains("id") || message["id"].toString().isEmpty()) {
        return false;
    }
    
    // 如果是目标更新消息，检查位置信息
    if (message.contains("latitude") || message.contains("longitude")) {
        if (!message.contains("latitude") || !message.contains("longitude")) {
            return false;
        }
        
        double lat = message["latitude"].toDouble();
        double lon = message["longitude"].toDouble();
        
        if (lat < -90.0 || lat > 90.0 || lon < -180.0 || lon > 180.0) {
            return false;
        }
    }
    
    return true;
}

QJsonObject JsonMessageParser::normalizeMessage(const QJsonObject& message) const
{
    QJsonObject normalized = message;
    
    // 确保有消息类型
    if (!normalized.contains("type")) {
        if (normalized.contains("latitude") && normalized.contains("longitude")) {
            normalized["type"] = "target_update";
        } else {
            normalized["type"] = "unknown";
        }
    }
    
    // 确保有时间戳
    if (!normalized.contains("timestamp")) {
        normalized["timestamp"] = QDateTime::currentDateTime().toString(Qt::ISODate);
    }
    
    // 标准化数值字段
    if (normalized.contains("latitude")) {
        normalized["latitude"] = normalized["latitude"].toDouble();
    }
    if (normalized.contains("longitude")) {
        normalized["longitude"] = normalized["longitude"].toDouble();
    }
    if (normalized.contains("altitude")) {
        normalized["altitude"] = normalized["altitude"].toDouble();
    }
    if (normalized.contains("heading")) {
        normalized["heading"] = normalized["heading"].toDouble();
    }
    if (normalized.contains("speed")) {
        normalized["speed"] = normalized["speed"].toDouble();
    }
    
    return normalized;
}

// BinaryMessageParser 实现
BinaryMessageParser::BinaryMessageParser()
{
}

QJsonObject BinaryMessageParser::parseMessage(const QByteArray& data)
{
    if (!isValidMessage(data)) {
        return QJsonObject();
    }
    
    QDataStream stream(data);
    stream.setByteOrder(QDataStream::LittleEndian);
    
    // 读取消息头
    MessageHeader header;
    stream >> header.magic >> header.version >> header.type >> header.length;
    
    if (!validateHeader(header, data.size())) {
        qWarning() << "Invalid binary message header";
        return QJsonObject();
    }
    
    // 根据消息类型解析数据
    switch (header.type) {
    case TARGET_UPDATE:
        return parseTargetUpdate(data.mid(sizeof(MessageHeader)));
    case TARGET_REMOVE:
        return parseTargetRemove(data.mid(sizeof(MessageHeader)));
    case HEARTBEAT:
        return parseHeartbeat(data.mid(sizeof(MessageHeader)));
    default:
        qWarning() << "Unknown binary message type:" << header.type;
        return QJsonObject();
    }
}

bool BinaryMessageParser::isValidMessage(const QByteArray& data)
{
    if (data.size() < static_cast<int>(sizeof(MessageHeader))) {
        return false;
    }
    
    QDataStream stream(data);
    stream.setByteOrder(QDataStream::LittleEndian);
    
    quint32 magic;
    stream >> magic;
    
    return magic == MAGIC_NUMBER;
}

QString BinaryMessageParser::parserName() const
{
    return "Binary Parser";
}

QStringList BinaryMessageParser::supportedTypes() const
{
    return QStringList() << "target_update" << "target_remove" << "heartbeat";
}

bool BinaryMessageParser::validateHeader(const MessageHeader& header, int dataSize) const
{
    return header.magic == MAGIC_NUMBER &&
           header.version == CURRENT_VERSION &&
           header.length <= static_cast<quint32>(dataSize - sizeof(MessageHeader));
}

QJsonObject BinaryMessageParser::parseTargetUpdate(const QByteArray& data) const
{
    if (data.size() < 32) { // 最小目标更新数据大小
        return QJsonObject();
    }
    
    QDataStream stream(data);
    stream.setByteOrder(QDataStream::LittleEndian);
    
    // 读取目标ID长度和ID
    quint16 idLength;
    stream >> idLength;
    
    if (idLength > 256 || data.size() < 2 + idLength + 30) {
        return QJsonObject();
    }
    
    QByteArray idData(idLength, 0);
    stream.readRawData(idData.data(), idLength);
    QString targetId = QString::fromUtf8(idData);
    
    // 读取位置和状态数据
    double latitude, longitude, altitude, heading, speed;
    stream >> latitude >> longitude >> altitude >> heading >> speed;
    
    QJsonObject message;
    message["type"] = "target_update";
    message["id"] = targetId;
    message["latitude"] = latitude;
    message["longitude"] = longitude;
    message["altitude"] = altitude;
    message["heading"] = heading;
    message["speed"] = speed;
    message["timestamp"] = QDateTime::currentDateTime().toString(Qt::ISODate);
    
    return message;
}

QJsonObject BinaryMessageParser::parseTargetRemove(const QByteArray& data) const
{
    if (data.size() < 2) {
        return QJsonObject();
    }
    
    QDataStream stream(data);
    stream.setByteOrder(QDataStream::LittleEndian);
    
    quint16 idLength;
    stream >> idLength;
    
    if (idLength > 256 || data.size() < 2 + idLength) {
        return QJsonObject();
    }
    
    QByteArray idData(idLength, 0);
    stream.readRawData(idData.data(), idLength);
    QString targetId = QString::fromUtf8(idData);
    
    QJsonObject message;
    message["type"] = "target_remove";
    message["id"] = targetId;
    message["timestamp"] = QDateTime::currentDateTime().toString(Qt::ISODate);
    
    return message;
}

QJsonObject BinaryMessageParser::parseHeartbeat(const QByteArray& data) const
{
    Q_UNUSED(data)
    
    QJsonObject message;
    message["type"] = "heartbeat";
    message["timestamp"] = QDateTime::currentDateTime().toString(Qt::ISODate);
    
    return message;
}
