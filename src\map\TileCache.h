#ifndef TILECACHE_H
#define TILECACHE_H

#include <QObject>
#include <QPixmap>
#include <QCache>
#include <QMutex>
#include <QTimer>

/**
 * @brief 瓦片缓存管理器
 * 
 * 实现LRU缓存策略的瓦片缓存管理
 */
class TileCache : public QObject
{
    Q_OBJECT

public:
    explicit TileCache(QObject *parent = nullptr);
    ~TileCache();
    
    // 缓存操作
    void insert(const QString& key, const QPixmap& pixmap);
    QPixmap* find(const QString& key);
    bool contains(const QString& key) const;
    void remove(const QString& key);
    void clear();
    
    // 缓存配置
    void setMaxCost(int maxCost);
    int maxCost() const;
    int totalCost() const;
    int count() const;
    
    // 统计信息
    quint64 hitCount() const;
    quint64 missCount() const;
    double hitRate() const;
    void resetStatistics();

signals:
    void cacheStatisticsChanged();

private slots:
    void updateStatistics();

private:
    void onItemRemoved(const QString& key, QPixmap* pixmap);

private:
    QCache<QString, QPixmap> m_cache;
    mutable QMutex m_mutex;
    QTimer* m_statisticsTimer;
    
    // 统计信息
    quint64 m_hitCount;
    quint64 m_missCount;
    
    // 常量
    static const int DEFAULT_MAX_COST;
    static const int STATISTICS_UPDATE_INTERVAL;
};

#endif // TILECACHE_H
