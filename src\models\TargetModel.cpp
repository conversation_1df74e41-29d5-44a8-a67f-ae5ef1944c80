#include "TargetModel.h"
#include <Qt>
#include <QIcon>
#include <QColor>
#include <QDateTime>
#include <QDebug>

// 列标题定义
const QStringList TargetModel::COLUMN_HEADERS = {
    QObject::tr("ID"),
    QObject::tr("名称"),
    QObject::tr("纬度"),
    QObject::tr("经度"),
    QObject::tr("高度"),
    QObject::tr("朝向"),
    QObject::tr("速度"),
    QObject::tr("类型"),
    QObject::tr("状态"),
    QObject::tr("更新时间")
};

TargetModel::TargetModel(QObject *parent)
    : QAbstractTableModel(parent)
{
}

TargetModel::~TargetModel()
{
    // 注意：不要在这里删除Target对象，它们由TargetManager管理
}

int TargetModel::rowCount(const QModelIndex &parent) const
{
    Q_UNUSED(parent)
    return m_targets.size();
}

int TargetModel::columnCount(const QModelIndex &parent) const
{
    Q_UNUSED(parent)
    return ColumnCount;
}

QVariant TargetModel::data(const QModelIndex &index, int role) const
{
    if (!index.isValid() || index.row() >= m_targets.size()) {
        return QVariant();
    }
    
    const Target* target = m_targets.at(index.row());
    if (!target) {
        return QVariant();
    }
    
    switch (role) {
    case Qt::DisplayRole:
        return getDisplayData(target, index.column());
    case Qt::TextAlignmentRole:
        return getAlignmentData(index.column());
    case Qt::BackgroundRole:
        return getBackgroundData(target, index.column());
    case Qt::ForegroundRole:
        return getForegroundData(target, index.column());
    case Qt::DecorationRole:
        return getDecorationData(target, index.column());
    case Qt::ToolTipRole:
        return getToolTipData(target, index.column());
    default:
        return QVariant();
    }
}

QVariant TargetModel::headerData(int section, Qt::Orientation orientation, int role) const
{
    if (orientation == Qt::Horizontal && role == Qt::DisplayRole) {
        if (section >= 0 && section < COLUMN_HEADERS.size()) {
            return COLUMN_HEADERS.at(section);
        }
    }
    
    return QAbstractTableModel::headerData(section, orientation, role);
}

Qt::ItemFlags TargetModel::flags(const QModelIndex &index) const
{
    if (!index.isValid()) {
        return Qt::NoItemFlags;
    }
    
    return Qt::ItemIsEnabled | Qt::ItemIsSelectable;
}

bool TargetModel::setData(const QModelIndex &index, const QVariant &value, int role)
{
    Q_UNUSED(index)
    Q_UNUSED(value)
    Q_UNUSED(role)
    
    // 目标数据是只读的，不允许直接编辑
    return false;
}

void TargetModel::addTarget(Target* target)
{
    if (!target || findTargetRow(target) >= 0) {
        return;
    }
    
    beginInsertRows(QModelIndex(), m_targets.size(), m_targets.size());
    m_targets.append(target);
    endInsertRows();
    
    qDebug() << "Target added to model:" << target->id;
}

void TargetModel::updateTarget(Target* target)
{
    int row = findTargetRow(target);
    if (row >= 0) {
        QModelIndex topLeft = index(row, 0);
        QModelIndex bottomRight = index(row, ColumnCount - 1);
        emit dataChanged(topLeft, bottomRight);
        
        qDebug() << "Target updated in model:" << target->id;
    }
}

void TargetModel::removeTarget(const QString& targetId)
{
    int row = findTargetRow(targetId);
    if (row >= 0) {
        beginRemoveRows(QModelIndex(), row, row);
        m_targets.removeAt(row);
        endRemoveRows();
        
        qDebug() << "Target removed from model:" << targetId;
    }
}

void TargetModel::removeTarget(Target* target)
{
    int row = findTargetRow(target);
    if (row >= 0) {
        beginRemoveRows(QModelIndex(), row, row);
        m_targets.removeAt(row);
        endRemoveRows();
        
        qDebug() << "Target removed from model:" << target->id;
    }
}

void TargetModel::clearAllTargets()
{
    beginResetModel();
    m_targets.clear();
    endResetModel();
    
    qDebug() << "All targets cleared from model";
}

Target* TargetModel::getTarget(int row) const
{
    if (row >= 0 && row < m_targets.size()) {
        return m_targets.at(row);
    }
    return nullptr;
}

Target* TargetModel::getTarget(const QString& targetId) const
{
    for (Target* target : m_targets) {
        if (target && target->id == targetId) {
            return target;
        }
    }
    return nullptr;
}

int TargetModel::findTargetRow(const QString& targetId) const
{
    for (int i = 0; i < m_targets.size(); ++i) {
        if (m_targets.at(i) && m_targets.at(i)->id == targetId) {
            return i;
        }
    }
    return -1;
}

int TargetModel::findTargetRow(Target* target) const
{
    return m_targets.indexOf(target);
}

void TargetModel::sort(int column, Qt::SortOrder order)
{
    if (column < 0 || column >= ColumnCount) {
        return;
    }
    
    beginResetModel();
    
    std::sort(m_targets.begin(), m_targets.end(), 
              [column, order](const Target* a, const Target* b) {
        if (!a || !b) return false;
        
        bool ascending = (order == Qt::AscendingOrder);
        
        switch (column) {
        case ColumnId:
            return ascending ? a->id < b->id : a->id > b->id;
        case ColumnName:
            return ascending ? a->name < b->name : a->name > b->name;
        case ColumnLatitude:
            return ascending ? a->latitude < b->latitude : a->latitude > b->latitude;
        case ColumnLongitude:
            return ascending ? a->longitude < b->longitude : a->longitude > b->longitude;
        case ColumnAltitude:
            return ascending ? a->altitude < b->altitude : a->altitude > b->altitude;
        case ColumnHeading:
            return ascending ? a->heading < b->heading : a->heading > b->heading;
        case ColumnSpeed:
            return ascending ? a->speed < b->speed : a->speed > b->speed;
        case ColumnType:
            return ascending ? a->type < b->type : a->type > b->type;
        case ColumnStatus:
            return ascending ? a->status < b->status : a->status > b->status;
        case ColumnLastUpdate:
            return ascending ? a->lastUpdate < b->lastUpdate : a->lastUpdate > b->lastUpdate;
        default:
            return false;
        }
    });
    
    endResetModel();
}

void TargetModel::setFilter(const QString& filter)
{
    m_filter = filter;
    // TODO: 实现过滤功能
}

QString TargetModel::getFilter() const
{
    return m_filter;
}

QVariant TargetModel::getDisplayData(const Target* target, int column) const
{
    switch (column) {
    case ColumnId:
        return target->id;
    case ColumnName:
        return target->name.isEmpty() ? tr("未命名") : target->name;
    case ColumnLatitude:
        return formatCoordinate(target->latitude);
    case ColumnLongitude:
        return formatCoordinate(target->longitude);
    case ColumnAltitude:
        return QString("%1 m").arg(target->altitude, 0, 'f', 1);
    case ColumnHeading:
        return formatHeading(target->heading);
    case ColumnSpeed:
        return formatSpeed(target->speed);
    case ColumnType:
        return target->type.isEmpty() ? tr("未知") : target->type;
    case ColumnStatus:
        return target->status.isEmpty() ? tr("未知") : target->status;
    case ColumnLastUpdate:
        return formatDateTime(target->lastUpdate);
    default:
        return QVariant();
    }
}

QVariant TargetModel::getAlignmentData(int column) const
{
    switch (column) {
    case ColumnLatitude:
    case ColumnLongitude:
    case ColumnAltitude:
    case ColumnHeading:
    case ColumnSpeed:
        return 1;
    default:
        return 0;
    }
}

QVariant TargetModel::getBackgroundData(const Target* target, int column) const
{
    Q_UNUSED(column)
    
    // 根据目标状态设置背景色
    if (target->status == "active") {
        return QColor(240, 255, 240); // 淡绿色
    } else if (target->status == "inactive") {
        return QColor(255, 240, 240); // 淡红色
    } else if (target->status == "warning") {
        return QColor(255, 255, 240); // 淡黄色
    }
    
    return QVariant();
}

QVariant TargetModel::getForegroundData(const Target* target, int column) const
{
    Q_UNUSED(column)
    
    // 根据目标状态设置前景色
    if (target->status == "inactive") {
        return QColor(128, 128, 128); // 灰色
    }
    
    return QVariant();
}

QVariant TargetModel::getDecorationData(const Target* target, int column) const
{
    if (column == ColumnStatus) {
        return getStatusIcon(target->status);
    }
    
    return QVariant();
}

QVariant TargetModel::getToolTipData(const Target* target, int column) const
{
    Q_UNUSED(column)
    
    return QString("ID: %1\n位置: %2, %3\n状态: %4\n更新: %5")
           .arg(target->id)
           .arg(formatCoordinate(target->latitude))
           .arg(formatCoordinate(target->longitude))
           .arg(target->status.isEmpty() ? tr("未知") : target->status)
           .arg(formatDateTime(target->lastUpdate));
}

QString TargetModel::formatCoordinate(double value, int precision) const
{
    return QString::number(value, 'f', precision);
}

QString TargetModel::formatSpeed(double speed) const
{
    return QString("%1 m/s").arg(speed, 0, 'f', 1);
}

QString TargetModel::formatHeading(double heading) const
{
    return QString("%1°").arg(heading, 0, 'f', 1);
}

QString TargetModel::formatDateTime(const QDateTime& dateTime) const
{
    return dateTime.toString("yyyy-MM-dd hh:mm:ss");
}

QIcon TargetModel::getStatusIcon(const QString& status) const
{
    // TODO: 实现状态图标
    Q_UNUSED(status)
    return QIcon();
}

QColor TargetModel::getStatusColor(const QString& status) const
{
    if (status == "active") {
        return Qt::green;
    } else if (status == "inactive") {
        return Qt::red;
    } else if (status == "warning") {
        return Qt::yellow;
    }
    
    return Qt::gray;
}
