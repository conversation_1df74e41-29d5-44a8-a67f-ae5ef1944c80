#include "MainWindow.h"
#include "map/MapWidget.h"
#include "managers/TargetManager.h"
#include "network/UdpReceiver.h"
#include "models/TargetModel.h"
#include "utils/ConfigManager.h"
#include "common/Constants.h"

#include <QApplication>
#include <QCloseEvent>
#include <QMessageBox>
#include <QSettings>
#include <QFileDialog>
#include <QDesktopServices>
#include <QUrl>

MainWindow::MainWindow(QWidget *parent)
    : QMainWindow(parent)
    , m_mapWidget(nullptr)
    , m_targetManager(nullptr)
    , m_udpReceiver(nullptr)
    , m_targetModel(nullptr)
    , m_centralWidget(nullptr)
    , m_mainLayout(nullptr)
    , m_mainSplitter(nullptr)
    , m_targetDock(nullptr)
    , m_targetView(nullptr)
{
    setWindowTitle(QString("%1 v%2").arg(Constants::APP_NAME, APP_VERSION));
    setMinimumSize(1024, 768);
    resize(1400, 900);
    
    setupUI();
    setupMenuBar();
    setupToolBar();
    setupStatusBar();
    setupDockWidgets();
    
    // 创建核心组件
    m_targetManager = new TargetManager(this);
    m_targetModel = m_targetManager->model();
    m_mapWidget = new MapWidget(this);
    m_udpReceiver = new UdpReceiver(this);
    
    // 设置中央组件
    setCentralWidget(m_mapWidget);
    
    // 设置目标视图模型
    m_targetView->setModel(m_targetModel);
    
    connectSignals();
    loadSettings();

    // 连接目标管理器和地图组件
    if (m_targetManager && m_mapWidget) {
        connect(m_targetManager, &TargetManager::targetAdded,
                this, [this](const QString& targetId) {
            Target* target = m_targetManager->getTarget(targetId);
            if (target) {
                m_mapWidget->addTarget(targetId, target->latitude, target->longitude);
            }
        });

        connect(m_targetManager, &TargetManager::targetUpdated,
                this, [this](const QString& targetId) {
            Target* target = m_targetManager->getTarget(targetId);
            if (target) {
                m_mapWidget->updateTarget(targetId, target->latitude, target->longitude);
            }
        });

        connect(m_targetManager, &TargetManager::targetRemoved,
                this, [this](const QString& targetId) {
            m_mapWidget->removeTarget(targetId);
        });
    }

    // 启动UDP接收器
    m_udpReceiver->startListening();
}

MainWindow::~MainWindow()
{
    saveSettings();
}

void MainWindow::setupUI()
{
    m_centralWidget = new QWidget(this);
    m_mainLayout = new QVBoxLayout(m_centralWidget);
    m_mainLayout->setContentsMargins(0, 0, 0, 0);
    
    m_mainSplitter = new QSplitter(Qt::Horizontal, this);
    m_mainLayout->addWidget(m_mainSplitter);
}

void MainWindow::setupMenuBar()
{
    // 文件菜单
    QMenu* fileMenu = menuBar()->addMenu(tr("文件(&F)"));
    
    m_openConfigAction = new QAction(tr("配置(&C)..."), this);
    m_openConfigAction->setShortcut(QKeySequence::Preferences);
    m_openConfigAction->setStatusTip(tr("打开配置对话框"));
    fileMenu->addAction(m_openConfigAction);
    
    fileMenu->addSeparator();
    
    m_exitAction = new QAction(tr("退出(&X)"), this);
    m_exitAction->setShortcut(QKeySequence::Quit);
    m_exitAction->setStatusTip(tr("退出应用程序"));
    fileMenu->addAction(m_exitAction);
    
    // 视图菜单
    QMenu* viewMenu = menuBar()->addMenu(tr("视图(&V)"));
    
    m_fullScreenAction = new QAction(tr("全屏(&F)"), this);
    m_fullScreenAction->setShortcut(QKeySequence::FullScreen);
    m_fullScreenAction->setCheckable(true);
    m_fullScreenAction->setStatusTip(tr("切换全屏模式"));
    viewMenu->addAction(m_fullScreenAction);
    
    m_resetViewAction = new QAction(tr("重置视图(&R)"), this);
    m_resetViewAction->setShortcut(QKeySequence("Ctrl+R"));
    m_resetViewAction->setStatusTip(tr("重置地图视图到默认位置"));
    viewMenu->addAction(m_resetViewAction);
    
    // 帮助菜单
    QMenu* helpMenu = menuBar()->addMenu(tr("帮助(&H)"));
    
    m_aboutAction = new QAction(tr("关于(&A)"), this);
    m_aboutAction->setStatusTip(tr("显示关于信息"));
    helpMenu->addAction(m_aboutAction);
}

void MainWindow::setupToolBar()
{
    m_toolBar = addToolBar(tr("主工具栏"));
    m_toolBar->setToolButtonStyle(Qt::ToolButtonTextUnderIcon);
    
    m_toolBar->addAction(m_openConfigAction);
    m_toolBar->addSeparator();
    m_toolBar->addAction(m_resetViewAction);
    m_toolBar->addAction(m_fullScreenAction);
}

void MainWindow::setupStatusBar()
{
    m_statusBar = statusBar();
    
    m_coordinateLabel = new QLabel(tr("坐标: --"));
    m_zoomLabel = new QLabel(tr("缩放: --"));
    m_targetCountLabel = new QLabel(tr("目标: 0"));
    m_networkStatusLabel = new QLabel(tr("网络: 断开"));
    m_loadingProgress = new QProgressBar();
    m_loadingProgress->setVisible(false);
    m_loadingProgress->setMaximumWidth(200);
    
    m_statusBar->addWidget(m_coordinateLabel);
    m_statusBar->addWidget(m_zoomLabel);
    m_statusBar->addWidget(m_targetCountLabel);
    m_statusBar->addPermanentWidget(m_networkStatusLabel);
    m_statusBar->addPermanentWidget(m_loadingProgress);
}

void MainWindow::setupDockWidgets()
{
    // 目标列表停靠窗口
    m_targetDock = new QDockWidget(tr("目标列表"), this);
    m_targetDock->setAllowedAreas(Qt::LeftDockWidgetArea | Qt::RightDockWidgetArea);
    
    m_targetView = new QTreeView();
    m_targetView->setAlternatingRowColors(true);
    m_targetView->setSelectionBehavior(QAbstractItemView::SelectRows);
    m_targetView->setRootIsDecorated(false);
    
    m_targetDock->setWidget(m_targetView);
    addDockWidget(Qt::RightDockWidgetArea, m_targetDock);
}

void MainWindow::connectSignals()
{
    // 菜单动作连接
    connect(m_openConfigAction, &QAction::triggered, this, &MainWindow::openConfig);
    connect(m_exitAction, &QAction::triggered, this, &QWidget::close);
    connect(m_aboutAction, &QAction::triggered, this, &MainWindow::showAbout);
    connect(m_fullScreenAction, &QAction::triggered, this, &MainWindow::toggleFullScreen);
    connect(m_resetViewAction, &QAction::triggered, this, &MainWindow::resetView);

    // 地图组件连接
    if (m_mapWidget) {
        connect(m_mapWidget, &MapWidget::centerChanged,
                this, &MainWindow::onMapCenterChanged);
        connect(m_mapWidget, &MapWidget::zoomLevelChanged,
                this, &MainWindow::onZoomLevelChanged);
        connect(m_mapWidget, &MapWidget::targetClicked,
                this, [this](const QString& targetId) {
            qDebug() << "Target clicked:" << targetId;
        });
    }

    // 目标管理器连接
    if (m_targetManager) {
        connect(m_targetManager, &TargetManager::targetAdded,
                this, &MainWindow::onTargetUpdated);
        connect(m_targetManager, &TargetManager::targetUpdated,
                this, &MainWindow::onTargetUpdated);
        connect(m_targetManager, &TargetManager::targetRemoved,
                this, &MainWindow::onTargetUpdated);
        connect(m_targetManager, &TargetManager::targetCountChanged,
                this, [this](int count) {
            m_targetCountLabel->setText(tr("目标: %1").arg(count));
        });
    }

    // UDP接收器连接
    if (m_udpReceiver) {
        connect(m_udpReceiver, &UdpReceiver::networkStatusChanged,
                this, &MainWindow::onNetworkStatusChanged);
        connect(m_udpReceiver, &UdpReceiver::targetDataReceived,
                m_targetManager, &TargetManager::onTargetDataReceived);
    }
}

void MainWindow::closeEvent(QCloseEvent *event)
{
    saveSettings();
    event->accept();
}

void MainWindow::onTargetUpdated(const QString& targetId)
{
    Q_UNUSED(targetId)
    if (m_targetModel) {
        m_targetCountLabel->setText(tr("目标: %1").arg(m_targetModel->rowCount()));
    }
}

void MainWindow::onNetworkStatusChanged(bool connected)
{
    m_networkStatusLabel->setText(connected ? tr("网络: 连接") : tr("网络: 断开"));
    m_networkStatusLabel->setStyleSheet(connected ? 
        "color: green;" : "color: red;");
}

void MainWindow::onMapCenterChanged(double latitude, double longitude)
{
    m_coordinateLabel->setText(tr("坐标: %1, %2")
        .arg(latitude, 0, 'f', 6)
        .arg(longitude, 0, 'f', 6));
}

void MainWindow::onZoomLevelChanged(int level)
{
    m_zoomLabel->setText(tr("缩放: %1").arg(level));
}

void MainWindow::openConfig()
{
    // TODO: 实现配置对话框
    QMessageBox::information(this, tr("配置"), tr("配置功能将在后续版本中实现"));
}

void MainWindow::showAbout()
{
    QMessageBox::about(this, tr("关于 %1").arg(Constants::APP_NAME),
        tr("<h3>%1 v%2</h3>"
           "<p>基于Qt的高性能离线地图应用程序</p>"
           "<p>支持UDP组播目标数据接收和实时显示</p>"
           "<p>Copyright © 2024 %3</p>")
        .arg(Constants::APP_NAME, APP_VERSION, Constants::APP_ORGANIZATION));
}

void MainWindow::toggleFullScreen()
{
    if (isFullScreen()) {
        showNormal();
        m_fullScreenAction->setChecked(false);
    } else {
        showFullScreen();
        m_fullScreenAction->setChecked(true);
    }
}

void MainWindow::resetView()
{
    if (m_mapWidget) {
        // TODO: 实现重置视图功能
    }
}

void MainWindow::loadSettings()
{
    QSettings settings;
    restoreGeometry(settings.value("geometry").toByteArray());
    restoreState(settings.value("windowState").toByteArray());
}

void MainWindow::saveSettings()
{
    QSettings settings;
    settings.setValue("geometry", saveGeometry());
    settings.setValue("windowState", saveState());
}
