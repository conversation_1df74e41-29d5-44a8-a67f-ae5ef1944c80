#############################################################################
# Makefile for building: QMapApp
# Generated by qmake (3.1) (Qt 5.14.2)
# Project:  QMapApp.pro
# Template: app
#############################################################################

MAKEFILE      = Makefile.Release

EQ            = =

####### Compiler, tools and options

CC            = gcc
CXX           = g++
DEFINES       = -DUNICODE -D_UNICODE -DWIN32 -DMINGW_HAS_SECURE_API=1 -DAPP_VERSION=\"1.0.0\" -DQT_NO_DEBUG_OUTPUT -DQT_NO_DEBUG -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_NETWORK_LIB -DQT_CONCURRENT_LIB -DQT_CORE_LIB -DQT_NEEDS_QMAIN
CFLAGS        = -fno-keep-inline-dllexport -O2 -Wall -Wextra -Wextra $(DEFINES)
CXXFLAGS      = -fno-keep-inline-dllexport /std:c++17 -O2 -std=gnu++1z -Wall -Wextra -Wextra -fexceptions -mthreads $(DEFINES)
INCPATH       = -I. -Isrc -I..\..\..\..\Softwares\Qt\5.14.2\mingw73_64\include -I..\..\..\..\Softwares\Qt\5.14.2\mingw73_64\include\QtWidgets -I..\..\..\..\Softwares\Qt\5.14.2\mingw73_64\include\QtGui -I..\..\..\..\Softwares\Qt\5.14.2\mingw73_64\include\QtANGLE -I..\..\..\..\Softwares\Qt\5.14.2\mingw73_64\include\QtNetwork -I..\..\..\..\Softwares\Qt\5.14.2\mingw73_64\include\QtConcurrent -I..\..\..\..\Softwares\Qt\5.14.2\mingw73_64\include\QtCore -Itmp -I/include -I..\..\..\..\Softwares\Qt\5.14.2\mingw73_64\mkspecs\win32-g++ 
LINKER      =        g++
LFLAGS        =        -Wl,-s -Wl,-subsystem,windows -mthreads
LIBS        =        D:\Softwares\Qt\5.14.2\mingw73_64\lib\libQt5Widgets.a D:\Softwares\Qt\5.14.2\mingw73_64\lib\libQt5Gui.a D:\Softwares\Qt\5.14.2\mingw73_64\lib\libQt5Network.a D:\Softwares\Qt\5.14.2\mingw73_64\lib\libQt5Concurrent.a D:\Softwares\Qt\5.14.2\mingw73_64\lib\libQt5Core.a D:\VMwareOS\ShareData\dky\xianshi\tmp\QMapApp_resource_res.o  -lmingw32 D:\Softwares\Qt\5.14.2\mingw73_64\lib\libqtmain.a -LC:\openssl\lib -LC:\Utils\my_sql\mysql-5.7.25-winx64\lib -LC:\Utils\postgresql\pgsql\lib -lshell32 
QMAKE         = D:\Softwares\Qt\5.14.2\mingw73_64\bin\qmake.exe
DEL_FILE      = del
CHK_DIR_EXISTS= if not exist
MKDIR         = mkdir
COPY          = copy /y
COPY_FILE     = copy /y
COPY_DIR      = xcopy /s /q /y /i
INSTALL_FILE  = copy /y
INSTALL_PROGRAM = copy /y
INSTALL_DIR   = xcopy /s /q /y /i
QINSTALL      = D:\Softwares\Qt\5.14.2\mingw73_64\bin\qmake.exe -install qinstall
QINSTALL_PROGRAM = D:\Softwares\Qt\5.14.2\mingw73_64\bin\qmake.exe -install qinstall -exe
DEL_FILE      = del
SYMLINK       = $(QMAKE) -install ln -f -s
DEL_DIR       = rmdir
MOVE          = move
IDC           = idc
IDL           = midl
ZIP           = zip -r -9
DEF_FILE      = 
RES_FILE      = D:\VMwareOS\ShareData\dky\xianshi\tmp\QMapApp_resource_res.o
SED           = $(QMAKE) -install sed
MOVE          = move

####### Output directory

OBJECTS_DIR   = tmp

####### Files

SOURCES       = src\main.cpp \
		src\MainWindow.cpp \
		src\map\MapWidget.cpp \
		src\map\TileLoader.cpp \
		src\map\TileCache.cpp \
		src\network\UdpReceiver.cpp \
		src\network\MessageParser.cpp \
		src\models\TargetModel.cpp \
		src\managers\TargetManager.cpp \
		src\utils\CoordinateConverter.cpp \
		src\utils\ConfigManager.cpp \
		src\utils\PerformanceMonitor.cpp \
		src\graphics\TargetItem.cpp tmp\qrc_resources.cpp \
		tmp\moc_MainWindow.cpp \
		tmp\moc_MapWidget.cpp \
		tmp\moc_TileLoader.cpp \
		tmp\moc_TileCache.cpp \
		tmp\moc_UdpReceiver.cpp \
		tmp\moc_TargetModel.cpp \
		tmp\moc_TargetManager.cpp \
		tmp\moc_ConfigManager.cpp \
		tmp\moc_PerformanceMonitor.cpp \
		tmp\moc_TargetItem.cpp
OBJECTS       = tmp/main.o \
		tmp/MainWindow.o \
		tmp/MapWidget.o \
		tmp/TileLoader.o \
		tmp/TileCache.o \
		tmp/UdpReceiver.o \
		tmp/MessageParser.o \
		tmp/TargetModel.o \
		tmp/TargetManager.o \
		tmp/CoordinateConverter.o \
		tmp/ConfigManager.o \
		tmp/PerformanceMonitor.o \
		tmp/TargetItem.o \
		tmp/qrc_resources.o \
		tmp/moc_MainWindow.o \
		tmp/moc_MapWidget.o \
		tmp/moc_TileLoader.o \
		tmp/moc_TileCache.o \
		tmp/moc_UdpReceiver.o \
		tmp/moc_TargetModel.o \
		tmp/moc_TargetManager.o \
		tmp/moc_ConfigManager.o \
		tmp/moc_PerformanceMonitor.o \
		tmp/moc_TargetItem.o

DIST          =  src\MainWindow.h \
		src\map\MapWidget.h \
		src\map\TileLoader.h \
		src\map\TileCache.h \
		src\network\UdpReceiver.h \
		src\network\IMessageParser.h \
		src\network\MessageParser.h \
		src\models\TargetModel.h \
		src\managers\TargetManager.h \
		src\utils\CoordinateConverter.h \
		src\utils\ConfigManager.h \
		src\utils\PerformanceMonitor.h \
		src\graphics\TargetItem.h \
		src\common\Target.h \
		src\common\Constants.h src\main.cpp \
		src\MainWindow.cpp \
		src\map\MapWidget.cpp \
		src\map\TileLoader.cpp \
		src\map\TileCache.cpp \
		src\network\UdpReceiver.cpp \
		src\network\MessageParser.cpp \
		src\models\TargetModel.cpp \
		src\managers\TargetManager.cpp \
		src\utils\CoordinateConverter.cpp \
		src\utils\ConfigManager.cpp \
		src\utils\PerformanceMonitor.cpp \
		src\graphics\TargetItem.cpp
QMAKE_TARGET  = QMapApp
DESTDIR        = bin\ #avoid trailing-slash linebreak
TARGET         = QMapApp.exe
DESTDIR_TARGET = bin\QMapApp.exe

####### Build rules

first: all
all: Makefile.Release  bin/QMapApp.exe

bin/QMapApp.exe: D:/Softwares/Qt/5.14.2/mingw73_64/lib/libQt5Widgets.a D:/Softwares/Qt/5.14.2/mingw73_64/lib/libQt5Gui.a D:/Softwares/Qt/5.14.2/mingw73_64/lib/libQt5Network.a D:/Softwares/Qt/5.14.2/mingw73_64/lib/libQt5Concurrent.a D:/Softwares/Qt/5.14.2/mingw73_64/lib/libQt5Core.a D:/Softwares/Qt/5.14.2/mingw73_64/lib/libqtmain.a $(OBJECTS) D:/VMwareOS/ShareData/dky/xianshi/tmp/QMapApp_resource_res.o
	$(LINKER) $(LFLAGS) -o $(DESTDIR_TARGET) @object_script.QMapApp.Release  $(LIBS)

D:/VMwareOS/ShareData/dky/xianshi/tmp/QMapApp_resource_res.o: QMapApp_resource.rc
	windres -i QMapApp_resource.rc -o D:\VMwareOS\ShareData\dky\xianshi\tmp\QMapApp_resource_res.o --include-dir=. $(DEFINES)

qmake: FORCE
	@$(QMAKE) -o Makefile.Release QMapApp.pro

qmake_all: FORCE

dist:
	$(ZIP) QMapApp.zip $(SOURCES) $(DIST) QMapApp.pro ..\..\..\..\Softwares\Qt\5.14.2\mingw73_64\mkspecs\features\spec_pre.prf ..\..\..\..\Softwares\Qt\5.14.2\mingw73_64\mkspecs\qdevice.pri ..\..\..\..\Softwares\Qt\5.14.2\mingw73_64\mkspecs\features\device_config.prf ..\..\..\..\Softwares\Qt\5.14.2\mingw73_64\mkspecs\common\sanitize.conf ..\..\..\..\Softwares\Qt\5.14.2\mingw73_64\mkspecs\common\gcc-base.conf ..\..\..\..\Softwares\Qt\5.14.2\mingw73_64\mkspecs\common\g++-base.conf ..\..\..\..\Softwares\Qt\5.14.2\mingw73_64\mkspecs\common\angle.conf ..\..\..\..\Softwares\Qt\5.14.2\mingw73_64\mkspecs\features\win32\windows_vulkan_sdk.prf ..\..\..\..\Softwares\Qt\5.14.2\mingw73_64\mkspecs\common\windows-vulkan.conf ..\..\..\..\Softwares\Qt\5.14.2\mingw73_64\mkspecs\common\g++-win32.conf ..\..\..\..\Softwares\Qt\5.14.2\mingw73_64\mkspecs\common\windows-desktop.conf ..\..\..\..\Softwares\Qt\5.14.2\mingw73_64\mkspecs\qconfig.pri ..\..\..\..\Softwares\Qt\5.14.2\mingw73_64\mkspecs\modules\qt_lib_3danimation.pri ..\..\..\..\Softwares\Qt\5.14.2\mingw73_64\mkspecs\modules\qt_lib_3danimation_private.pri ..\..\..\..\Softwares\Qt\5.14.2\mingw73_64\mkspecs\modules\qt_lib_3dcore.pri ..\..\..\..\Softwares\Qt\5.14.2\mingw73_64\mkspecs\modules\qt_lib_3dcore_private.pri ..\..\..\..\Softwares\Qt\5.14.2\mingw73_64\mkspecs\modules\qt_lib_3dextras.pri ..\..\..\..\Softwares\Qt\5.14.2\mingw73_64\mkspecs\modules\qt_lib_3dextras_private.pri ..\..\..\..\Softwares\Qt\5.14.2\mingw73_64\mkspecs\modules\qt_lib_3dinput.pri ..\..\..\..\Softwares\Qt\5.14.2\mingw73_64\mkspecs\modules\qt_lib_3dinput_private.pri ..\..\..\..\Softwares\Qt\5.14.2\mingw73_64\mkspecs\modules\qt_lib_3dlogic.pri ..\..\..\..\Softwares\Qt\5.14.2\mingw73_64\mkspecs\modules\qt_lib_3dlogic_private.pri ..\..\..\..\Softwares\Qt\5.14.2\mingw73_64\mkspecs\modules\qt_lib_3dquick.pri ..\..\..\..\Softwares\Qt\5.14.2\mingw73_64\mkspecs\modules\qt_lib_3dquick_private.pri ..\..\..\..\Softwares\Qt\5.14.2\mingw73_64\mkspecs\modules\qt_lib_3dquickanimation.pri ..\..\..\..\Softwares\Qt\5.14.2\mingw73_64\mkspecs\modules\qt_lib_3dquickanimation_private.pri ..\..\..\..\Softwares\Qt\5.14.2\mingw73_64\mkspecs\modules\qt_lib_3dquickextras.pri ..\..\..\..\Softwares\Qt\5.14.2\mingw73_64\mkspecs\modules\qt_lib_3dquickextras_private.pri ..\..\..\..\Softwares\Qt\5.14.2\mingw73_64\mkspecs\modules\qt_lib_3dquickinput.pri ..\..\..\..\Softwares\Qt\5.14.2\mingw73_64\mkspecs\modules\qt_lib_3dquickinput_private.pri ..\..\..\..\Softwares\Qt\5.14.2\mingw73_64\mkspecs\modules\qt_lib_3dquickrender.pri ..\..\..\..\Softwares\Qt\5.14.2\mingw73_64\mkspecs\modules\qt_lib_3dquickrender_private.pri ..\..\..\..\Softwares\Qt\5.14.2\mingw73_64\mkspecs\modules\qt_lib_3dquickscene2d.pri ..\..\..\..\Softwares\Qt\5.14.2\mingw73_64\mkspecs\modules\qt_lib_3dquickscene2d_private.pri ..\..\..\..\Softwares\Qt\5.14.2\mingw73_64\mkspecs\modules\qt_lib_3drender.pri ..\..\..\..\Softwares\Qt\5.14.2\mingw73_64\mkspecs\modules\qt_lib_3drender_private.pri ..\..\..\..\Softwares\Qt\5.14.2\mingw73_64\mkspecs\modules\qt_lib_accessibility_support_private.pri ..\..\..\..\Softwares\Qt\5.14.2\mingw73_64\mkspecs\modules\qt_lib_axbase.pri ..\..\..\..\Softwares\Qt\5.14.2\mingw73_64\mkspecs\modules\qt_lib_axbase_private.pri ..\..\..\..\Softwares\Qt\5.14.2\mingw73_64\mkspecs\modules\qt_lib_axcontainer.pri ..\..\..\..\Softwares\Qt\5.14.2\mingw73_64\mkspecs\modules\qt_lib_axcontainer_private.pri ..\..\..\..\Softwares\Qt\5.14.2\mingw73_64\mkspecs\modules\qt_lib_axserver.pri ..\..\..\..\Softwares\Qt\5.14.2\mingw73_64\mkspecs\modules\qt_lib_axserver_private.pri ..\..\..\..\Softwares\Qt\5.14.2\mingw73_64\mkspecs\modules\qt_lib_bluetooth.pri ..\..\..\..\Softwares\Qt\5.14.2\mingw73_64\mkspecs\modules\qt_lib_bluetooth_private.pri ..\..\..\..\Softwares\Qt\5.14.2\mingw73_64\mkspecs\modules\qt_lib_bodymovin_private.pri ..\..\..\..\Softwares\Qt\5.14.2\mingw73_64\mkspecs\modules\qt_lib_bootstrap_private.pri ..\..\..\..\Softwares\Qt\5.14.2\mingw73_64\mkspecs\modules\qt_lib_charts.pri ..\..\..\..\Softwares\Qt\5.14.2\mingw73_64\mkspecs\modules\qt_lib_charts_private.pri ..\..\..\..\Softwares\Qt\5.14.2\mingw73_64\mkspecs\modules\qt_lib_concurrent.pri ..\..\..\..\Softwares\Qt\5.14.2\mingw73_64\mkspecs\modules\qt_lib_concurrent_private.pri ..\..\..\..\Softwares\Qt\5.14.2\mingw73_64\mkspecs\modules\qt_lib_core.pri ..\..\..\..\Softwares\Qt\5.14.2\mingw73_64\mkspecs\modules\qt_lib_core_private.pri ..\..\..\..\Softwares\Qt\5.14.2\mingw73_64\mkspecs\modules\qt_lib_datavisualization.pri ..\..\..\..\Softwares\Qt\5.14.2\mingw73_64\mkspecs\modules\qt_lib_datavisualization_private.pri ..\..\..\..\Softwares\Qt\5.14.2\mingw73_64\mkspecs\modules\qt_lib_dbus.pri ..\..\..\..\Softwares\Qt\5.14.2\mingw73_64\mkspecs\modules\qt_lib_dbus_private.pri ..\..\..\..\Softwares\Qt\5.14.2\mingw73_64\mkspecs\modules\qt_lib_designer.pri ..\..\..\..\Softwares\Qt\5.14.2\mingw73_64\mkspecs\modules\qt_lib_designer_private.pri ..\..\..\..\Softwares\Qt\5.14.2\mingw73_64\mkspecs\modules\qt_lib_designercomponents_private.pri ..\..\..\..\Softwares\Qt\5.14.2\mingw73_64\mkspecs\modules\qt_lib_devicediscovery_support_private.pri ..\..\..\..\Softwares\Qt\5.14.2\mingw73_64\mkspecs\modules\qt_lib_edid_support_private.pri ..\..\..\..\Softwares\Qt\5.14.2\mingw73_64\mkspecs\modules\qt_lib_egl_support_private.pri ..\..\..\..\Softwares\Qt\5.14.2\mingw73_64\mkspecs\modules\qt_lib_eventdispatcher_support_private.pri ..\..\..\..\Softwares\Qt\5.14.2\mingw73_64\mkspecs\modules\qt_lib_fb_support_private.pri ..\..\..\..\Softwares\Qt\5.14.2\mingw73_64\mkspecs\modules\qt_lib_fontdatabase_support_private.pri ..\..\..\..\Softwares\Qt\5.14.2\mingw73_64\mkspecs\modules\qt_lib_gamepad.pri ..\..\..\..\Softwares\Qt\5.14.2\mingw73_64\mkspecs\modules\qt_lib_gamepad_private.pri ..\..\..\..\Softwares\Qt\5.14.2\mingw73_64\mkspecs\modules\qt_lib_gui.pri ..\..\..\..\Softwares\Qt\5.14.2\mingw73_64\mkspecs\modules\qt_lib_gui_private.pri ..\..\..\..\Softwares\Qt\5.14.2\mingw73_64\mkspecs\modules\qt_lib_help.pri ..\..\..\..\Softwares\Qt\5.14.2\mingw73_64\mkspecs\modules\qt_lib_help_private.pri ..\..\..\..\Softwares\Qt\5.14.2\mingw73_64\mkspecs\modules\qt_lib_location.pri ..\..\..\..\Softwares\Qt\5.14.2\mingw73_64\mkspecs\modules\qt_lib_location_private.pri ..\..\..\..\Softwares\Qt\5.14.2\mingw73_64\mkspecs\modules\qt_lib_multimedia.pri ..\..\..\..\Softwares\Qt\5.14.2\mingw73_64\mkspecs\modules\qt_lib_multimedia_private.pri ..\..\..\..\Softwares\Qt\5.14.2\mingw73_64\mkspecs\modules\qt_lib_multimediawidgets.pri ..\..\..\..\Softwares\Qt\5.14.2\mingw73_64\mkspecs\modules\qt_lib_multimediawidgets_private.pri ..\..\..\..\Softwares\Qt\5.14.2\mingw73_64\mkspecs\modules\qt_lib_network.pri ..\..\..\..\Softwares\Qt\5.14.2\mingw73_64\mkspecs\modules\qt_lib_network_private.pri ..\..\..\..\Softwares\Qt\5.14.2\mingw73_64\mkspecs\modules\qt_lib_networkauth.pri ..\..\..\..\Softwares\Qt\5.14.2\mingw73_64\mkspecs\modules\qt_lib_networkauth_private.pri ..\..\..\..\Softwares\Qt\5.14.2\mingw73_64\mkspecs\modules\qt_lib_nfc.pri ..\..\..\..\Softwares\Qt\5.14.2\mingw73_64\mkspecs\modules\qt_lib_nfc_private.pri ..\..\..\..\Softwares\Qt\5.14.2\mingw73_64\mkspecs\modules\qt_lib_opengl.pri ..\..\..\..\Softwares\Qt\5.14.2\mingw73_64\mkspecs\modules\qt_lib_opengl_private.pri ..\..\..\..\Softwares\Qt\5.14.2\mingw73_64\mkspecs\modules\qt_lib_openglextensions.pri ..\..\..\..\Softwares\Qt\5.14.2\mingw73_64\mkspecs\modules\qt_lib_openglextensions_private.pri ..\..\..\..\Softwares\Qt\5.14.2\mingw73_64\mkspecs\modules\qt_lib_packetprotocol_private.pri ..\..\..\..\Softwares\Qt\5.14.2\mingw73_64\mkspecs\modules\qt_lib_platformcompositor_support_private.pri ..\..\..\..\Softwares\Qt\5.14.2\mingw73_64\mkspecs\modules\qt_lib_positioning.pri ..\..\..\..\Softwares\Qt\5.14.2\mingw73_64\mkspecs\modules\qt_lib_positioning_private.pri ..\..\..\..\Softwares\Qt\5.14.2\mingw73_64\mkspecs\modules\qt_lib_positioningquick.pri ..\..\..\..\Softwares\Qt\5.14.2\mingw73_64\mkspecs\modules\qt_lib_positioningquick_private.pri ..\..\..\..\Softwares\Qt\5.14.2\mingw73_64\mkspecs\modules\qt_lib_printsupport.pri ..\..\..\..\Softwares\Qt\5.14.2\mingw73_64\mkspecs\modules\qt_lib_printsupport_private.pri ..\..\..\..\Softwares\Qt\5.14.2\mingw73_64\mkspecs\modules\qt_lib_purchasing.pri ..\..\..\..\Softwares\Qt\5.14.2\mingw73_64\mkspecs\modules\qt_lib_purchasing_private.pri ..\..\..\..\Softwares\Qt\5.14.2\mingw73_64\mkspecs\modules\qt_lib_qml.pri ..\..\..\..\Softwares\Qt\5.14.2\mingw73_64\mkspecs\modules\qt_lib_qml_private.pri ..\..\..\..\Softwares\Qt\5.14.2\mingw73_64\mkspecs\modules\qt_lib_qmldebug_private.pri ..\..\..\..\Softwares\Qt\5.14.2\mingw73_64\mkspecs\modules\qt_lib_qmldevtools_private.pri ..\..\..\..\Softwares\Qt\5.14.2\mingw73_64\mkspecs\modules\qt_lib_qmlmodels.pri ..\..\..\..\Softwares\Qt\5.14.2\mingw73_64\mkspecs\modules\qt_lib_qmlmodels_private.pri ..\..\..\..\Softwares\Qt\5.14.2\mingw73_64\mkspecs\modules\qt_lib_qmltest.pri ..\..\..\..\Softwares\Qt\5.14.2\mingw73_64\mkspecs\modules\qt_lib_qmltest_private.pri ..\..\..\..\Softwares\Qt\5.14.2\mingw73_64\mkspecs\modules\qt_lib_qmlworkerscript.pri ..\..\..\..\Softwares\Qt\5.14.2\mingw73_64\mkspecs\modules\qt_lib_qmlworkerscript_private.pri ..\..\..\..\Softwares\Qt\5.14.2\mingw73_64\mkspecs\modules\qt_lib_qtmultimediaquicktools_private.pri ..\..\..\..\Softwares\Qt\5.14.2\mingw73_64\mkspecs\modules\qt_lib_quick.pri ..\..\..\..\Softwares\Qt\5.14.2\mingw73_64\mkspecs\modules\qt_lib_quick3d.pri ..\..\..\..\Softwares\Qt\5.14.2\mingw73_64\mkspecs\modules\qt_lib_quick3d_private.pri ..\..\..\..\Softwares\Qt\5.14.2\mingw73_64\mkspecs\modules\qt_lib_quick3dassetimport.pri ..\..\..\..\Softwares\Qt\5.14.2\mingw73_64\mkspecs\modules\qt_lib_quick3dassetimport_private.pri ..\..\..\..\Softwares\Qt\5.14.2\mingw73_64\mkspecs\modules\qt_lib_quick3drender.pri ..\..\..\..\Softwares\Qt\5.14.2\mingw73_64\mkspecs\modules\qt_lib_quick3drender_private.pri ..\..\..\..\Softwares\Qt\5.14.2\mingw73_64\mkspecs\modules\qt_lib_quick3druntimerender.pri ..\..\..\..\Softwares\Qt\5.14.2\mingw73_64\mkspecs\modules\qt_lib_quick3druntimerender_private.pri ..\..\..\..\Softwares\Qt\5.14.2\mingw73_64\mkspecs\modules\qt_lib_quick3dutils.pri ..\..\..\..\Softwares\Qt\5.14.2\mingw73_64\mkspecs\modules\qt_lib_quick3dutils_private.pri ..\..\..\..\Softwares\Qt\5.14.2\mingw73_64\mkspecs\modules\qt_lib_quick_private.pri ..\..\..\..\Softwares\Qt\5.14.2\mingw73_64\mkspecs\modules\qt_lib_quickcontrols2.pri ..\..\..\..\Softwares\Qt\5.14.2\mingw73_64\mkspecs\modules\qt_lib_quickcontrols2_private.pri ..\..\..\..\Softwares\Qt\5.14.2\mingw73_64\mkspecs\modules\qt_lib_quickparticles_private.pri ..\..\..\..\Softwares\Qt\5.14.2\mingw73_64\mkspecs\modules\qt_lib_quickshapes_private.pri ..\..\..\..\Softwares\Qt\5.14.2\mingw73_64\mkspecs\modules\qt_lib_quicktemplates2.pri ..\..\..\..\Softwares\Qt\5.14.2\mingw73_64\mkspecs\modules\qt_lib_quicktemplates2_private.pri ..\..\..\..\Softwares\Qt\5.14.2\mingw73_64\mkspecs\modules\qt_lib_quickwidgets.pri ..\..\..\..\Softwares\Qt\5.14.2\mingw73_64\mkspecs\modules\qt_lib_quickwidgets_private.pri ..\..\..\..\Softwares\Qt\5.14.2\mingw73_64\mkspecs\modules\qt_lib_remoteobjects.pri ..\..\..\..\Softwares\Qt\5.14.2\mingw73_64\mkspecs\modules\qt_lib_remoteobjects_private.pri ..\..\..\..\Softwares\Qt\5.14.2\mingw73_64\mkspecs\modules\qt_lib_repparser.pri ..\..\..\..\Softwares\Qt\5.14.2\mingw73_64\mkspecs\modules\qt_lib_repparser_private.pri ..\..\..\..\Softwares\Qt\5.14.2\mingw73_64\mkspecs\modules\qt_lib_script.pri ..\..\..\..\Softwares\Qt\5.14.2\mingw73_64\mkspecs\modules\qt_lib_script_private.pri ..\..\..\..\Softwares\Qt\5.14.2\mingw73_64\mkspecs\modules\qt_lib_scripttools.pri ..\..\..\..\Softwares\Qt\5.14.2\mingw73_64\mkspecs\modules\qt_lib_scripttools_private.pri ..\..\..\..\Softwares\Qt\5.14.2\mingw73_64\mkspecs\modules\qt_lib_scxml.pri ..\..\..\..\Softwares\Qt\5.14.2\mingw73_64\mkspecs\modules\qt_lib_scxml_private.pri ..\..\..\..\Softwares\Qt\5.14.2\mingw73_64\mkspecs\modules\qt_lib_sensors.pri ..\..\..\..\Softwares\Qt\5.14.2\mingw73_64\mkspecs\modules\qt_lib_sensors_private.pri ..\..\..\..\Softwares\Qt\5.14.2\mingw73_64\mkspecs\modules\qt_lib_serialbus.pri ..\..\..\..\Softwares\Qt\5.14.2\mingw73_64\mkspecs\modules\qt_lib_serialbus_private.pri ..\..\..\..\Softwares\Qt\5.14.2\mingw73_64\mkspecs\modules\qt_lib_serialport.pri ..\..\..\..\Softwares\Qt\5.14.2\mingw73_64\mkspecs\modules\qt_lib_serialport_private.pri ..\..\..\..\Softwares\Qt\5.14.2\mingw73_64\mkspecs\modules\qt_lib_sql.pri ..\..\..\..\Softwares\Qt\5.14.2\mingw73_64\mkspecs\modules\qt_lib_sql_private.pri ..\..\..\..\Softwares\Qt\5.14.2\mingw73_64\mkspecs\modules\qt_lib_svg.pri ..\..\..\..\Softwares\Qt\5.14.2\mingw73_64\mkspecs\modules\qt_lib_svg_private.pri ..\..\..\..\Softwares\Qt\5.14.2\mingw73_64\mkspecs\modules\qt_lib_testlib.pri ..\..\..\..\Softwares\Qt\5.14.2\mingw73_64\mkspecs\modules\qt_lib_testlib_private.pri ..\..\..\..\Softwares\Qt\5.14.2\mingw73_64\mkspecs\modules\qt_lib_texttospeech.pri ..\..\..\..\Softwares\Qt\5.14.2\mingw73_64\mkspecs\modules\qt_lib_texttospeech_private.pri ..\..\..\..\Softwares\Qt\5.14.2\mingw73_64\mkspecs\modules\qt_lib_theme_support_private.pri ..\..\..\..\Softwares\Qt\5.14.2\mingw73_64\mkspecs\modules\qt_lib_uiplugin.pri ..\..\..\..\Softwares\Qt\5.14.2\mingw73_64\mkspecs\modules\qt_lib_uitools.pri ..\..\..\..\Softwares\Qt\5.14.2\mingw73_64\mkspecs\modules\qt_lib_uitools_private.pri ..\..\..\..\Softwares\Qt\5.14.2\mingw73_64\mkspecs\modules\qt_lib_virtualkeyboard.pri ..\..\..\..\Softwares\Qt\5.14.2\mingw73_64\mkspecs\modules\qt_lib_virtualkeyboard_private.pri ..\..\..\..\Softwares\Qt\5.14.2\mingw73_64\mkspecs\modules\qt_lib_vulkan_support_private.pri ..\..\..\..\Softwares\Qt\5.14.2\mingw73_64\mkspecs\modules\qt_lib_webchannel.pri ..\..\..\..\Softwares\Qt\5.14.2\mingw73_64\mkspecs\modules\qt_lib_webchannel_private.pri ..\..\..\..\Softwares\Qt\5.14.2\mingw73_64\mkspecs\modules\qt_lib_websockets.pri ..\..\..\..\Softwares\Qt\5.14.2\mingw73_64\mkspecs\modules\qt_lib_websockets_private.pri ..\..\..\..\Softwares\Qt\5.14.2\mingw73_64\mkspecs\modules\qt_lib_widgets.pri ..\..\..\..\Softwares\Qt\5.14.2\mingw73_64\mkspecs\modules\qt_lib_widgets_private.pri ..\..\..\..\Softwares\Qt\5.14.2\mingw73_64\mkspecs\modules\qt_lib_windowsuiautomation_support_private.pri ..\..\..\..\Softwares\Qt\5.14.2\mingw73_64\mkspecs\modules\qt_lib_winextras.pri ..\..\..\..\Softwares\Qt\5.14.2\mingw73_64\mkspecs\modules\qt_lib_winextras_private.pri ..\..\..\..\Softwares\Qt\5.14.2\mingw73_64\mkspecs\modules\qt_lib_xml.pri ..\..\..\..\Softwares\Qt\5.14.2\mingw73_64\mkspecs\modules\qt_lib_xml_private.pri ..\..\..\..\Softwares\Qt\5.14.2\mingw73_64\mkspecs\modules\qt_lib_xmlpatterns.pri ..\..\..\..\Softwares\Qt\5.14.2\mingw73_64\mkspecs\modules\qt_lib_xmlpatterns_private.pri ..\..\..\..\Softwares\Qt\5.14.2\mingw73_64\mkspecs\modules\qt_lib_zlib_private.pri ..\..\..\..\Softwares\Qt\5.14.2\mingw73_64\mkspecs\features\qt_functions.prf ..\..\..\..\Softwares\Qt\5.14.2\mingw73_64\mkspecs\features\qt_config.prf ..\..\..\..\Softwares\Qt\5.14.2\mingw73_64\mkspecs\win32-g++\qmake.conf ..\..\..\..\Softwares\Qt\5.14.2\mingw73_64\mkspecs\features\spec_post.prf .qmake.stash ..\..\..\..\Softwares\Qt\5.14.2\mingw73_64\mkspecs\features\exclusive_builds.prf ..\..\..\..\Softwares\Qt\5.14.2\mingw73_64\mkspecs\features\toolchain.prf ..\..\..\..\Softwares\Qt\5.14.2\mingw73_64\mkspecs\features\default_pre.prf ..\..\..\..\Softwares\Qt\5.14.2\mingw73_64\mkspecs\features\win32\default_pre.prf ..\..\..\..\Softwares\Qt\5.14.2\mingw73_64\mkspecs\features\resolve_config.prf ..\..\..\..\Softwares\Qt\5.14.2\mingw73_64\mkspecs\features\exclusive_builds_post.prf ..\..\..\..\Softwares\Qt\5.14.2\mingw73_64\mkspecs\features\default_post.prf ..\..\..\..\Softwares\Qt\5.14.2\mingw73_64\mkspecs\features\build_pass.prf ..\..\..\..\Softwares\Qt\5.14.2\mingw73_64\mkspecs\features\precompile_header.prf ..\..\..\..\Softwares\Qt\5.14.2\mingw73_64\mkspecs\features\warn_on.prf ..\..\..\..\Softwares\Qt\5.14.2\mingw73_64\mkspecs\features\qt.prf ..\..\..\..\Softwares\Qt\5.14.2\mingw73_64\mkspecs\features\resources_functions.prf ..\..\..\..\Softwares\Qt\5.14.2\mingw73_64\mkspecs\features\resources.prf ..\..\..\..\Softwares\Qt\5.14.2\mingw73_64\mkspecs\features\moc.prf ..\..\..\..\Softwares\Qt\5.14.2\mingw73_64\mkspecs\features\win32\opengl.prf ..\..\..\..\Softwares\Qt\5.14.2\mingw73_64\mkspecs\features\uic.prf ..\..\..\..\Softwares\Qt\5.14.2\mingw73_64\mkspecs\features\qmake_use.prf ..\..\..\..\Softwares\Qt\5.14.2\mingw73_64\mkspecs\features\file_copies.prf ..\..\..\..\Softwares\Qt\5.14.2\mingw73_64\mkspecs\features\win32\windows.prf ..\..\..\..\Softwares\Qt\5.14.2\mingw73_64\mkspecs\features\testcase_targets.prf ..\..\..\..\Softwares\Qt\5.14.2\mingw73_64\mkspecs\features\exceptions.prf ..\..\..\..\Softwares\Qt\5.14.2\mingw73_64\mkspecs\features\yacc.prf ..\..\..\..\Softwares\Qt\5.14.2\mingw73_64\mkspecs\features\lex.prf QMapApp.pro resources\resources.qrc ..\..\..\..\Softwares\Qt\5.14.2\mingw73_64\lib\Qt5Widgets.prl ..\..\..\..\Softwares\Qt\5.14.2\mingw73_64\lib\Qt5Gui.prl ..\..\..\..\Softwares\Qt\5.14.2\mingw73_64\lib\Qt5Network.prl ..\..\..\..\Softwares\Qt\5.14.2\mingw73_64\lib\Qt5Concurrent.prl ..\..\..\..\Softwares\Qt\5.14.2\mingw73_64\lib\Qt5Core.prl ..\..\..\..\Softwares\Qt\5.14.2\mingw73_64\lib\qtmain.prl   resources\resources.qrc ..\..\..\..\Softwares\Qt\5.14.2\mingw73_64\mkspecs\features\data\dummy.cpp src\MainWindow.h src\map\MapWidget.h src\map\TileLoader.h src\map\TileCache.h src\network\UdpReceiver.h src\network\IMessageParser.h src\network\MessageParser.h src\models\TargetModel.h src\managers\TargetManager.h src\utils\CoordinateConverter.h src\utils\ConfigManager.h src\utils\PerformanceMonitor.h src\graphics\TargetItem.h src\common\Target.h src\common\Constants.h  src\main.cpp src\MainWindow.cpp src\map\MapWidget.cpp src\map\TileLoader.cpp src\map\TileCache.cpp src\network\UdpReceiver.cpp src\network\MessageParser.cpp src\models\TargetModel.cpp src\managers\TargetManager.cpp src\utils\CoordinateConverter.cpp src\utils\ConfigManager.cpp src\utils\PerformanceMonitor.cpp src\graphics\TargetItem.cpp     

clean: compiler_clean 
	-$(DEL_FILE) tmp\main.o tmp\MainWindow.o tmp\MapWidget.o tmp\TileLoader.o tmp\TileCache.o tmp\UdpReceiver.o tmp\MessageParser.o tmp\TargetModel.o tmp\TargetManager.o tmp\CoordinateConverter.o tmp\ConfigManager.o tmp\PerformanceMonitor.o tmp\TargetItem.o tmp\qrc_resources.o tmp\moc_MainWindow.o tmp\moc_MapWidget.o tmp\moc_TileLoader.o tmp\moc_TileCache.o tmp\moc_UdpReceiver.o tmp\moc_TargetModel.o tmp\moc_TargetManager.o tmp\moc_ConfigManager.o tmp\moc_PerformanceMonitor.o tmp\moc_TargetItem.o
	-$(DEL_FILE) D:\VMwareOS\ShareData\dky\xianshi\tmp\QMapApp_resource_res.o

distclean: clean 
	-$(DEL_FILE) .qmake.stash
	-$(DEL_FILE) $(DESTDIR_TARGET)
	-$(DEL_FILE) Makefile.Release

mocclean: compiler_moc_header_clean compiler_moc_objc_header_clean compiler_moc_source_clean

mocables: compiler_moc_header_make_all compiler_moc_objc_header_make_all compiler_moc_source_make_all

check: first

benchmark: first

compiler_no_pch_compiler_make_all:
compiler_no_pch_compiler_clean:
compiler_rcc_make_all: tmp/qrc_resources.cpp
compiler_rcc_clean:
	-$(DEL_FILE) tmp\qrc_resources.cpp
tmp/qrc_resources.cpp: resources/resources.qrc \
		../../../../Softwares/Qt/5.14.2/mingw73_64/bin/rcc.exe \
		resources/config/appconfig.json \
		resources/icons/app.png \
		resources/icons/target.png \
		resources/icons/reset.png \
		resources/icons/fullscreen.png \
		resources/icons/config.png
	D:\Softwares\Qt\5.14.2\mingw73_64\bin\rcc.exe -name resources resources\resources.qrc -o tmp\qrc_resources.cpp

compiler_moc_predefs_make_all: tmp/moc_predefs.h
compiler_moc_predefs_clean:
	-$(DEL_FILE) tmp\moc_predefs.h
tmp/moc_predefs.h: ../../../../Softwares/Qt/5.14.2/mingw73_64/mkspecs/features/data/dummy.cpp
	g++ -fno-keep-inline-dllexport /std:c++17 -O2 -std=gnu++1z -Wall -Wextra -Wextra -dM -E -o tmp\moc_predefs.h ..\..\..\..\Softwares\Qt\5.14.2\mingw73_64\mkspecs\features\data\dummy.cpp

compiler_moc_header_make_all: tmp/moc_MainWindow.cpp tmp/moc_MapWidget.cpp tmp/moc_TileLoader.cpp tmp/moc_TileCache.cpp tmp/moc_UdpReceiver.cpp tmp/moc_TargetModel.cpp tmp/moc_TargetManager.cpp tmp/moc_ConfigManager.cpp tmp/moc_PerformanceMonitor.cpp tmp/moc_TargetItem.cpp
compiler_moc_header_clean:
	-$(DEL_FILE) tmp\moc_MainWindow.cpp tmp\moc_MapWidget.cpp tmp\moc_TileLoader.cpp tmp\moc_TileCache.cpp tmp\moc_UdpReceiver.cpp tmp\moc_TargetModel.cpp tmp\moc_TargetManager.cpp tmp\moc_ConfigManager.cpp tmp\moc_PerformanceMonitor.cpp tmp\moc_TargetItem.cpp
tmp/moc_MainWindow.cpp: src/MainWindow.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/QMainWindow \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/qmainwindow.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/qtwidgetsglobal.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qtguiglobal.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qglobal.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qconfig-bootstrapped.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qconfig.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qtcore-config.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qsystemdetection.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qprocessordetection.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qcompilerdetection.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qtypeinfo.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qsysinfo.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qlogging.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qflags.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qatomic.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qbasicatomic.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qatomic_bootstrap.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qgenericatomic.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qatomic_cxx11.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qatomic_msvc.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qglobalstatic.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qmutex.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qnumeric.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qversiontagging.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qtgui-config.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/qtwidgets-config.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/qwidget.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qwindowdefs.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qobjectdefs.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qnamespace.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qobjectdefs_impl.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qwindowdefs_win.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qobject.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qstring.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qchar.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qbytearray.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qrefcount.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qarraydata.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qstringliteral.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qstringalgorithms.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qstringview.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qstringbuilder.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qlist.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qalgorithms.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qiterator.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qhashfunctions.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qpair.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qvector.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qcontainertools_impl.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qpoint.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qbytearraylist.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qstringlist.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qregexp.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qstringmatcher.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qcoreevent.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qscopedpointer.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qmetatype.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qvarlengtharray.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qcontainerfwd.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qobject_impl.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qmargins.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qpaintdevice.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qrect.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qsize.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qpalette.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qcolor.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qrgb.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qrgba64.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qbrush.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qmatrix.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qpolygon.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qregion.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qdatastream.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qiodevice.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qline.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qtransform.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qpainterpath.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qimage.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qpixelformat.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qpixmap.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qsharedpointer.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qshareddata.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qhash.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qsharedpointer_impl.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qfont.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qfontmetrics.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qfontinfo.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/qsizepolicy.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qcursor.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qkeysequence.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qevent.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qvariant.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qmap.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qdebug.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qtextstream.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qlocale.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qset.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qcontiguouscache.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qurl.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qurlquery.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qfile.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qfiledevice.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qvector2d.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qtouchdevice.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/qtabwidget.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qicon.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/QVBoxLayout \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/qboxlayout.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/qlayout.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/qlayoutitem.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/qgridlayout.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/QHBoxLayout \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/QMenuBar \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/qmenubar.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/qmenu.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/qaction.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/qactiongroup.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/QStatusBar \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/qstatusbar.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/QToolBar \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/qtoolbar.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/QAction \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/QLabel \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/qlabel.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/qframe.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/QProgressBar \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/qprogressbar.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/QSplitter \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/qsplitter.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/QDockWidget \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/qdockwidget.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/QTreeView \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/qtreeview.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/qabstractitemview.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/qabstractscrollarea.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qabstractitemmodel.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qitemselectionmodel.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/qabstractitemdelegate.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/qstyleoption.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/qabstractspinbox.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qvalidator.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qregularexpression.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/qslider.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/qabstractslider.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/qstyle.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/qtabbar.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/qrubberband.h \
		tmp/moc_predefs.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/bin/moc.exe
	D:\Softwares\Qt\5.14.2\mingw73_64\bin\moc.exe $(DEFINES) --include D:/VMwareOS/ShareData/dky/xianshi/tmp/moc_predefs.h -ID:/Softwares/Qt/5.14.2/mingw73_64/mkspecs/win32-g++ -ID:/VMwareOS/ShareData/dky/xianshi -ID:/VMwareOS/ShareData/dky/xianshi/src -ID:/Softwares/Qt/5.14.2/mingw73_64/include -ID:/Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets -ID:/Softwares/Qt/5.14.2/mingw73_64/include/QtGui -ID:/Softwares/Qt/5.14.2/mingw73_64/include/QtANGLE -ID:/Softwares/Qt/5.14.2/mingw73_64/include/QtNetwork -ID:/Softwares/Qt/5.14.2/mingw73_64/include/QtConcurrent -ID:/Softwares/Qt/5.14.2/mingw73_64/include/QtCore -ID:/Softwares/Qt/Tools/mingw730_64/lib/gcc/x86_64-w64-mingw32/7.3.0/include/c++ -ID:/Softwares/Qt/Tools/mingw730_64/lib/gcc/x86_64-w64-mingw32/7.3.0/include/c++/x86_64-w64-mingw32 -ID:/Softwares/Qt/Tools/mingw730_64/lib/gcc/x86_64-w64-mingw32/7.3.0/include/c++/backward -ID:/Softwares/Qt/Tools/mingw730_64/lib/gcc/x86_64-w64-mingw32/7.3.0/include -ID:/Softwares/Qt/Tools/mingw730_64/lib/gcc/x86_64-w64-mingw32/7.3.0/include-fixed -ID:/Softwares/Qt/Tools/mingw730_64/x86_64-w64-mingw32/include src\MainWindow.h -o tmp\moc_MainWindow.cpp

tmp/moc_MapWidget.cpp: src/map/MapWidget.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/QGraphicsView \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/qgraphicsview.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/qtwidgetsglobal.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qtguiglobal.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qglobal.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qconfig-bootstrapped.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qconfig.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qtcore-config.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qsystemdetection.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qprocessordetection.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qcompilerdetection.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qtypeinfo.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qsysinfo.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qlogging.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qflags.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qatomic.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qbasicatomic.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qatomic_bootstrap.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qgenericatomic.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qatomic_cxx11.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qatomic_msvc.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qglobalstatic.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qmutex.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qnumeric.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qversiontagging.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qtgui-config.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/qtwidgets-config.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qmetatype.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qbytearray.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qrefcount.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qnamespace.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qarraydata.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qvarlengtharray.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qcontainerfwd.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qalgorithms.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qcontainertools_impl.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qhashfunctions.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qstring.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qchar.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qstringliteral.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qstringalgorithms.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qstringview.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qstringbuilder.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qpair.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qobjectdefs.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qobjectdefs_impl.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qpainter.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qrect.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qmargins.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qsize.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qpoint.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qscopedpointer.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qpixmap.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qpaintdevice.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qwindowdefs.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qwindowdefs_win.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qcolor.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qrgb.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qstringlist.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qlist.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qiterator.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qvector.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qbytearraylist.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qregexp.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qstringmatcher.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qrgba64.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qsharedpointer.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qshareddata.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qhash.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qsharedpointer_impl.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qobject.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qcoreevent.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qobject_impl.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qimage.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qpixelformat.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qtransform.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qmatrix.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qpolygon.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qregion.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qdatastream.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qiodevice.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qline.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qpainterpath.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qtextoption.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qpen.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qbrush.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qfontinfo.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qfont.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qfontmetrics.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/qscrollarea.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/qabstractscrollarea.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/qframe.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/qwidget.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qpalette.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/qsizepolicy.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qcursor.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qkeysequence.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qevent.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qvariant.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qmap.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qdebug.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qtextstream.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qlocale.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qset.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qcontiguouscache.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qurl.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qurlquery.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qfile.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qfiledevice.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qvector2d.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qtouchdevice.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/qgraphicsscene.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/QGraphicsScene \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/QWheelEvent \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/QMouseEvent \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/QKeyEvent \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/QTimer \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qtimer.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qbasictimer.h \
		tmp/moc_predefs.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/bin/moc.exe
	D:\Softwares\Qt\5.14.2\mingw73_64\bin\moc.exe $(DEFINES) --include D:/VMwareOS/ShareData/dky/xianshi/tmp/moc_predefs.h -ID:/Softwares/Qt/5.14.2/mingw73_64/mkspecs/win32-g++ -ID:/VMwareOS/ShareData/dky/xianshi -ID:/VMwareOS/ShareData/dky/xianshi/src -ID:/Softwares/Qt/5.14.2/mingw73_64/include -ID:/Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets -ID:/Softwares/Qt/5.14.2/mingw73_64/include/QtGui -ID:/Softwares/Qt/5.14.2/mingw73_64/include/QtANGLE -ID:/Softwares/Qt/5.14.2/mingw73_64/include/QtNetwork -ID:/Softwares/Qt/5.14.2/mingw73_64/include/QtConcurrent -ID:/Softwares/Qt/5.14.2/mingw73_64/include/QtCore -ID:/Softwares/Qt/Tools/mingw730_64/lib/gcc/x86_64-w64-mingw32/7.3.0/include/c++ -ID:/Softwares/Qt/Tools/mingw730_64/lib/gcc/x86_64-w64-mingw32/7.3.0/include/c++/x86_64-w64-mingw32 -ID:/Softwares/Qt/Tools/mingw730_64/lib/gcc/x86_64-w64-mingw32/7.3.0/include/c++/backward -ID:/Softwares/Qt/Tools/mingw730_64/lib/gcc/x86_64-w64-mingw32/7.3.0/include -ID:/Softwares/Qt/Tools/mingw730_64/lib/gcc/x86_64-w64-mingw32/7.3.0/include-fixed -ID:/Softwares/Qt/Tools/mingw730_64/x86_64-w64-mingw32/include src\map\MapWidget.h -o tmp\moc_MapWidget.cpp

tmp/moc_TileLoader.cpp: src/map/TileLoader.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/QObject \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qobject.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qobjectdefs.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qnamespace.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qglobal.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qconfig-bootstrapped.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qconfig.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qtcore-config.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qsystemdetection.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qprocessordetection.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qcompilerdetection.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qtypeinfo.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qsysinfo.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qlogging.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qflags.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qatomic.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qbasicatomic.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qatomic_bootstrap.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qgenericatomic.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qatomic_cxx11.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qatomic_msvc.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qglobalstatic.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qmutex.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qnumeric.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qversiontagging.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qobjectdefs_impl.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qstring.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qchar.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qbytearray.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qrefcount.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qarraydata.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qstringliteral.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qstringalgorithms.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qstringview.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qstringbuilder.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qlist.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qalgorithms.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qiterator.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qhashfunctions.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qpair.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qvector.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qcontainertools_impl.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qpoint.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qbytearraylist.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qstringlist.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qregexp.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qstringmatcher.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qcoreevent.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qscopedpointer.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qmetatype.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qvarlengtharray.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qcontainerfwd.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qobject_impl.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/QThreadPool \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qthreadpool.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qthread.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qrunnable.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/QRunnable \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/QPixmap \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qpixmap.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qtguiglobal.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qtgui-config.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qpaintdevice.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qwindowdefs.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qwindowdefs_win.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qrect.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qmargins.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qsize.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qcolor.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qrgb.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qrgba64.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qsharedpointer.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qshareddata.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qhash.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qsharedpointer_impl.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qimage.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qpixelformat.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qtransform.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qmatrix.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qpolygon.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qregion.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qdatastream.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qiodevice.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qline.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qpainterpath.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/QRect \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/QSet \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qset.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/QMutex \
		tmp/moc_predefs.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/bin/moc.exe
	D:\Softwares\Qt\5.14.2\mingw73_64\bin\moc.exe $(DEFINES) --include D:/VMwareOS/ShareData/dky/xianshi/tmp/moc_predefs.h -ID:/Softwares/Qt/5.14.2/mingw73_64/mkspecs/win32-g++ -ID:/VMwareOS/ShareData/dky/xianshi -ID:/VMwareOS/ShareData/dky/xianshi/src -ID:/Softwares/Qt/5.14.2/mingw73_64/include -ID:/Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets -ID:/Softwares/Qt/5.14.2/mingw73_64/include/QtGui -ID:/Softwares/Qt/5.14.2/mingw73_64/include/QtANGLE -ID:/Softwares/Qt/5.14.2/mingw73_64/include/QtNetwork -ID:/Softwares/Qt/5.14.2/mingw73_64/include/QtConcurrent -ID:/Softwares/Qt/5.14.2/mingw73_64/include/QtCore -ID:/Softwares/Qt/Tools/mingw730_64/lib/gcc/x86_64-w64-mingw32/7.3.0/include/c++ -ID:/Softwares/Qt/Tools/mingw730_64/lib/gcc/x86_64-w64-mingw32/7.3.0/include/c++/x86_64-w64-mingw32 -ID:/Softwares/Qt/Tools/mingw730_64/lib/gcc/x86_64-w64-mingw32/7.3.0/include/c++/backward -ID:/Softwares/Qt/Tools/mingw730_64/lib/gcc/x86_64-w64-mingw32/7.3.0/include -ID:/Softwares/Qt/Tools/mingw730_64/lib/gcc/x86_64-w64-mingw32/7.3.0/include-fixed -ID:/Softwares/Qt/Tools/mingw730_64/x86_64-w64-mingw32/include src\map\TileLoader.h -o tmp\moc_TileLoader.cpp

tmp/moc_TileCache.cpp: src/map/TileCache.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/QObject \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qobject.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qobjectdefs.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qnamespace.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qglobal.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qconfig-bootstrapped.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qconfig.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qtcore-config.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qsystemdetection.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qprocessordetection.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qcompilerdetection.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qtypeinfo.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qsysinfo.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qlogging.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qflags.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qatomic.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qbasicatomic.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qatomic_bootstrap.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qgenericatomic.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qatomic_cxx11.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qatomic_msvc.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qglobalstatic.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qmutex.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qnumeric.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qversiontagging.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qobjectdefs_impl.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qstring.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qchar.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qbytearray.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qrefcount.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qarraydata.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qstringliteral.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qstringalgorithms.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qstringview.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qstringbuilder.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qlist.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qalgorithms.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qiterator.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qhashfunctions.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qpair.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qvector.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qcontainertools_impl.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qpoint.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qbytearraylist.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qstringlist.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qregexp.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qstringmatcher.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qcoreevent.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qscopedpointer.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qmetatype.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qvarlengtharray.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qcontainerfwd.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qobject_impl.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/QPixmap \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qpixmap.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qtguiglobal.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qtgui-config.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qpaintdevice.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qwindowdefs.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qwindowdefs_win.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qrect.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qmargins.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qsize.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qcolor.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qrgb.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qrgba64.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qsharedpointer.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qshareddata.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qhash.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qsharedpointer_impl.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qimage.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qpixelformat.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qtransform.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qmatrix.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qpolygon.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qregion.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qdatastream.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qiodevice.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qline.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qpainterpath.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/QCache \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qcache.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/QMutex \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/QTimer \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qtimer.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qbasictimer.h \
		tmp/moc_predefs.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/bin/moc.exe
	D:\Softwares\Qt\5.14.2\mingw73_64\bin\moc.exe $(DEFINES) --include D:/VMwareOS/ShareData/dky/xianshi/tmp/moc_predefs.h -ID:/Softwares/Qt/5.14.2/mingw73_64/mkspecs/win32-g++ -ID:/VMwareOS/ShareData/dky/xianshi -ID:/VMwareOS/ShareData/dky/xianshi/src -ID:/Softwares/Qt/5.14.2/mingw73_64/include -ID:/Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets -ID:/Softwares/Qt/5.14.2/mingw73_64/include/QtGui -ID:/Softwares/Qt/5.14.2/mingw73_64/include/QtANGLE -ID:/Softwares/Qt/5.14.2/mingw73_64/include/QtNetwork -ID:/Softwares/Qt/5.14.2/mingw73_64/include/QtConcurrent -ID:/Softwares/Qt/5.14.2/mingw73_64/include/QtCore -ID:/Softwares/Qt/Tools/mingw730_64/lib/gcc/x86_64-w64-mingw32/7.3.0/include/c++ -ID:/Softwares/Qt/Tools/mingw730_64/lib/gcc/x86_64-w64-mingw32/7.3.0/include/c++/x86_64-w64-mingw32 -ID:/Softwares/Qt/Tools/mingw730_64/lib/gcc/x86_64-w64-mingw32/7.3.0/include/c++/backward -ID:/Softwares/Qt/Tools/mingw730_64/lib/gcc/x86_64-w64-mingw32/7.3.0/include -ID:/Softwares/Qt/Tools/mingw730_64/lib/gcc/x86_64-w64-mingw32/7.3.0/include-fixed -ID:/Softwares/Qt/Tools/mingw730_64/x86_64-w64-mingw32/include src\map\TileCache.h -o tmp\moc_TileCache.cpp

tmp/moc_UdpReceiver.cpp: src/network/UdpReceiver.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/QObject \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qobject.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qobjectdefs.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qnamespace.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qglobal.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qconfig-bootstrapped.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qconfig.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qtcore-config.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qsystemdetection.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qprocessordetection.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qcompilerdetection.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qtypeinfo.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qsysinfo.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qlogging.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qflags.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qatomic.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qbasicatomic.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qatomic_bootstrap.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qgenericatomic.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qatomic_cxx11.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qatomic_msvc.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qglobalstatic.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qmutex.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qnumeric.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qversiontagging.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qobjectdefs_impl.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qstring.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qchar.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qbytearray.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qrefcount.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qarraydata.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qstringliteral.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qstringalgorithms.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qstringview.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qstringbuilder.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qlist.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qalgorithms.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qiterator.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qhashfunctions.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qpair.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qvector.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qcontainertools_impl.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qpoint.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qbytearraylist.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qstringlist.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qregexp.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qstringmatcher.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qcoreevent.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qscopedpointer.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qmetatype.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qvarlengtharray.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qcontainerfwd.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qobject_impl.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtNetwork/QUdpSocket \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtNetwork/qudpsocket.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtNetwork/qtnetworkglobal.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtNetwork/qtnetwork-config.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtNetwork/qabstractsocket.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qiodevice.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qdebug.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qhash.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qmap.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qtextstream.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qlocale.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qvariant.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qshareddata.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qset.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qcontiguouscache.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qsharedpointer.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qsharedpointer_impl.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtNetwork/qhostaddress.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtNetwork/QHostAddress \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/QTimer \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qtimer.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qbasictimer.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/QJsonObject \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qjsonobject.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qjsonvalue.h \
		tmp/moc_predefs.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/bin/moc.exe
	D:\Softwares\Qt\5.14.2\mingw73_64\bin\moc.exe $(DEFINES) --include D:/VMwareOS/ShareData/dky/xianshi/tmp/moc_predefs.h -ID:/Softwares/Qt/5.14.2/mingw73_64/mkspecs/win32-g++ -ID:/VMwareOS/ShareData/dky/xianshi -ID:/VMwareOS/ShareData/dky/xianshi/src -ID:/Softwares/Qt/5.14.2/mingw73_64/include -ID:/Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets -ID:/Softwares/Qt/5.14.2/mingw73_64/include/QtGui -ID:/Softwares/Qt/5.14.2/mingw73_64/include/QtANGLE -ID:/Softwares/Qt/5.14.2/mingw73_64/include/QtNetwork -ID:/Softwares/Qt/5.14.2/mingw73_64/include/QtConcurrent -ID:/Softwares/Qt/5.14.2/mingw73_64/include/QtCore -ID:/Softwares/Qt/Tools/mingw730_64/lib/gcc/x86_64-w64-mingw32/7.3.0/include/c++ -ID:/Softwares/Qt/Tools/mingw730_64/lib/gcc/x86_64-w64-mingw32/7.3.0/include/c++/x86_64-w64-mingw32 -ID:/Softwares/Qt/Tools/mingw730_64/lib/gcc/x86_64-w64-mingw32/7.3.0/include/c++/backward -ID:/Softwares/Qt/Tools/mingw730_64/lib/gcc/x86_64-w64-mingw32/7.3.0/include -ID:/Softwares/Qt/Tools/mingw730_64/lib/gcc/x86_64-w64-mingw32/7.3.0/include-fixed -ID:/Softwares/Qt/Tools/mingw730_64/x86_64-w64-mingw32/include src\network\UdpReceiver.h -o tmp\moc_UdpReceiver.cpp

tmp/moc_TargetModel.cpp: src/models/TargetModel.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/QAbstractTableModel \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qabstractitemmodel.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qvariant.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qatomic.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qglobal.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qconfig-bootstrapped.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qconfig.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qtcore-config.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qsystemdetection.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qprocessordetection.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qcompilerdetection.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qtypeinfo.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qsysinfo.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qlogging.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qflags.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qglobalstatic.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qmutex.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qnumeric.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qversiontagging.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qbasicatomic.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qatomic_bootstrap.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qgenericatomic.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qatomic_cxx11.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qatomic_msvc.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qbytearray.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qrefcount.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qnamespace.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qarraydata.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qlist.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qalgorithms.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qiterator.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qhashfunctions.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qstring.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qchar.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qstringliteral.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qstringalgorithms.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qstringview.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qstringbuilder.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qpair.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qvector.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qcontainertools_impl.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qpoint.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qbytearraylist.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qstringlist.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qregexp.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qstringmatcher.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qmetatype.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qvarlengtharray.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qcontainerfwd.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qobjectdefs.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qobjectdefs_impl.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qmap.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qdebug.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qhash.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qtextstream.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qiodevice.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qobject.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qcoreevent.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qscopedpointer.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qobject_impl.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qlocale.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qshareddata.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qset.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qcontiguouscache.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qsharedpointer.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qsharedpointer_impl.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/QList \
		src/common/Target.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/QString \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/QPointF \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/QDateTime \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qdatetime.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/QJsonObject \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qjsonobject.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qjsonvalue.h \
		tmp/moc_predefs.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/bin/moc.exe
	D:\Softwares\Qt\5.14.2\mingw73_64\bin\moc.exe $(DEFINES) --include D:/VMwareOS/ShareData/dky/xianshi/tmp/moc_predefs.h -ID:/Softwares/Qt/5.14.2/mingw73_64/mkspecs/win32-g++ -ID:/VMwareOS/ShareData/dky/xianshi -ID:/VMwareOS/ShareData/dky/xianshi/src -ID:/Softwares/Qt/5.14.2/mingw73_64/include -ID:/Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets -ID:/Softwares/Qt/5.14.2/mingw73_64/include/QtGui -ID:/Softwares/Qt/5.14.2/mingw73_64/include/QtANGLE -ID:/Softwares/Qt/5.14.2/mingw73_64/include/QtNetwork -ID:/Softwares/Qt/5.14.2/mingw73_64/include/QtConcurrent -ID:/Softwares/Qt/5.14.2/mingw73_64/include/QtCore -ID:/Softwares/Qt/Tools/mingw730_64/lib/gcc/x86_64-w64-mingw32/7.3.0/include/c++ -ID:/Softwares/Qt/Tools/mingw730_64/lib/gcc/x86_64-w64-mingw32/7.3.0/include/c++/x86_64-w64-mingw32 -ID:/Softwares/Qt/Tools/mingw730_64/lib/gcc/x86_64-w64-mingw32/7.3.0/include/c++/backward -ID:/Softwares/Qt/Tools/mingw730_64/lib/gcc/x86_64-w64-mingw32/7.3.0/include -ID:/Softwares/Qt/Tools/mingw730_64/lib/gcc/x86_64-w64-mingw32/7.3.0/include-fixed -ID:/Softwares/Qt/Tools/mingw730_64/x86_64-w64-mingw32/include src\models\TargetModel.h -o tmp\moc_TargetModel.cpp

tmp/moc_TargetManager.cpp: src/managers/TargetManager.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/QObject \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qobject.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qobjectdefs.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qnamespace.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qglobal.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qconfig-bootstrapped.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qconfig.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qtcore-config.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qsystemdetection.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qprocessordetection.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qcompilerdetection.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qtypeinfo.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qsysinfo.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qlogging.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qflags.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qatomic.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qbasicatomic.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qatomic_bootstrap.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qgenericatomic.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qatomic_cxx11.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qatomic_msvc.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qglobalstatic.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qmutex.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qnumeric.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qversiontagging.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qobjectdefs_impl.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qstring.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qchar.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qbytearray.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qrefcount.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qarraydata.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qstringliteral.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qstringalgorithms.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qstringview.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qstringbuilder.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qlist.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qalgorithms.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qiterator.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qhashfunctions.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qpair.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qvector.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qcontainertools_impl.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qpoint.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qbytearraylist.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qstringlist.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qregexp.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qstringmatcher.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qcoreevent.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qscopedpointer.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qmetatype.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qvarlengtharray.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qcontainerfwd.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qobject_impl.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/QMap \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qmap.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qdebug.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qhash.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qtextstream.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qiodevice.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qlocale.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qvariant.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qshareddata.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qset.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qcontiguouscache.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qsharedpointer.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qsharedpointer_impl.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/QTimer \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qtimer.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qbasictimer.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/QJsonObject \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qjsonobject.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qjsonvalue.h \
		src/common/Target.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/QString \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/QPointF \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/QDateTime \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qdatetime.h \
		tmp/moc_predefs.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/bin/moc.exe
	D:\Softwares\Qt\5.14.2\mingw73_64\bin\moc.exe $(DEFINES) --include D:/VMwareOS/ShareData/dky/xianshi/tmp/moc_predefs.h -ID:/Softwares/Qt/5.14.2/mingw73_64/mkspecs/win32-g++ -ID:/VMwareOS/ShareData/dky/xianshi -ID:/VMwareOS/ShareData/dky/xianshi/src -ID:/Softwares/Qt/5.14.2/mingw73_64/include -ID:/Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets -ID:/Softwares/Qt/5.14.2/mingw73_64/include/QtGui -ID:/Softwares/Qt/5.14.2/mingw73_64/include/QtANGLE -ID:/Softwares/Qt/5.14.2/mingw73_64/include/QtNetwork -ID:/Softwares/Qt/5.14.2/mingw73_64/include/QtConcurrent -ID:/Softwares/Qt/5.14.2/mingw73_64/include/QtCore -ID:/Softwares/Qt/Tools/mingw730_64/lib/gcc/x86_64-w64-mingw32/7.3.0/include/c++ -ID:/Softwares/Qt/Tools/mingw730_64/lib/gcc/x86_64-w64-mingw32/7.3.0/include/c++/x86_64-w64-mingw32 -ID:/Softwares/Qt/Tools/mingw730_64/lib/gcc/x86_64-w64-mingw32/7.3.0/include/c++/backward -ID:/Softwares/Qt/Tools/mingw730_64/lib/gcc/x86_64-w64-mingw32/7.3.0/include -ID:/Softwares/Qt/Tools/mingw730_64/lib/gcc/x86_64-w64-mingw32/7.3.0/include-fixed -ID:/Softwares/Qt/Tools/mingw730_64/x86_64-w64-mingw32/include src\managers\TargetManager.h -o tmp\moc_TargetManager.cpp

tmp/moc_ConfigManager.cpp: src/utils/ConfigManager.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/QObject \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qobject.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qobjectdefs.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qnamespace.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qglobal.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qconfig-bootstrapped.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qconfig.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qtcore-config.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qsystemdetection.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qprocessordetection.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qcompilerdetection.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qtypeinfo.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qsysinfo.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qlogging.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qflags.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qatomic.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qbasicatomic.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qatomic_bootstrap.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qgenericatomic.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qatomic_cxx11.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qatomic_msvc.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qglobalstatic.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qmutex.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qnumeric.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qversiontagging.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qobjectdefs_impl.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qstring.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qchar.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qbytearray.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qrefcount.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qarraydata.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qstringliteral.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qstringalgorithms.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qstringview.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qstringbuilder.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qlist.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qalgorithms.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qiterator.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qhashfunctions.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qpair.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qvector.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qcontainertools_impl.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qpoint.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qbytearraylist.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qstringlist.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qregexp.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qstringmatcher.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qcoreevent.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qscopedpointer.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qmetatype.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qvarlengtharray.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qcontainerfwd.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qobject_impl.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/QJsonObject \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qjsonobject.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qjsonvalue.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/QJsonDocument \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qjsondocument.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtNetwork/QHostAddress \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtNetwork/qhostaddress.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtNetwork/qtnetworkglobal.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtNetwork/qtnetwork-config.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qshareddata.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qhash.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtNetwork/qabstractsocket.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qiodevice.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qdebug.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qmap.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qtextstream.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qlocale.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qvariant.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qset.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qcontiguouscache.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qsharedpointer.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qsharedpointer_impl.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/QPointF \
		tmp/moc_predefs.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/bin/moc.exe
	D:\Softwares\Qt\5.14.2\mingw73_64\bin\moc.exe $(DEFINES) --include D:/VMwareOS/ShareData/dky/xianshi/tmp/moc_predefs.h -ID:/Softwares/Qt/5.14.2/mingw73_64/mkspecs/win32-g++ -ID:/VMwareOS/ShareData/dky/xianshi -ID:/VMwareOS/ShareData/dky/xianshi/src -ID:/Softwares/Qt/5.14.2/mingw73_64/include -ID:/Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets -ID:/Softwares/Qt/5.14.2/mingw73_64/include/QtGui -ID:/Softwares/Qt/5.14.2/mingw73_64/include/QtANGLE -ID:/Softwares/Qt/5.14.2/mingw73_64/include/QtNetwork -ID:/Softwares/Qt/5.14.2/mingw73_64/include/QtConcurrent -ID:/Softwares/Qt/5.14.2/mingw73_64/include/QtCore -ID:/Softwares/Qt/Tools/mingw730_64/lib/gcc/x86_64-w64-mingw32/7.3.0/include/c++ -ID:/Softwares/Qt/Tools/mingw730_64/lib/gcc/x86_64-w64-mingw32/7.3.0/include/c++/x86_64-w64-mingw32 -ID:/Softwares/Qt/Tools/mingw730_64/lib/gcc/x86_64-w64-mingw32/7.3.0/include/c++/backward -ID:/Softwares/Qt/Tools/mingw730_64/lib/gcc/x86_64-w64-mingw32/7.3.0/include -ID:/Softwares/Qt/Tools/mingw730_64/lib/gcc/x86_64-w64-mingw32/7.3.0/include-fixed -ID:/Softwares/Qt/Tools/mingw730_64/x86_64-w64-mingw32/include src\utils\ConfigManager.h -o tmp\moc_ConfigManager.cpp

tmp/moc_PerformanceMonitor.cpp: src/utils/PerformanceMonitor.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/QObject \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qobject.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qobjectdefs.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qnamespace.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qglobal.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qconfig-bootstrapped.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qconfig.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qtcore-config.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qsystemdetection.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qprocessordetection.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qcompilerdetection.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qtypeinfo.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qsysinfo.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qlogging.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qflags.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qatomic.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qbasicatomic.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qatomic_bootstrap.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qgenericatomic.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qatomic_cxx11.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qatomic_msvc.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qglobalstatic.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qmutex.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qnumeric.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qversiontagging.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qobjectdefs_impl.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qstring.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qchar.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qbytearray.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qrefcount.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qarraydata.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qstringliteral.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qstringalgorithms.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qstringview.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qstringbuilder.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qlist.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qalgorithms.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qiterator.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qhashfunctions.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qpair.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qvector.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qcontainertools_impl.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qpoint.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qbytearraylist.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qstringlist.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qregexp.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qstringmatcher.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qcoreevent.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qscopedpointer.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qmetatype.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qvarlengtharray.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qcontainerfwd.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qobject_impl.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/QTimer \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qtimer.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qbasictimer.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/QElapsedTimer \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qelapsedtimer.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/QMap \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qmap.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qdebug.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qhash.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qtextstream.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qiodevice.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qlocale.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qvariant.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qshareddata.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qset.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qcontiguouscache.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qsharedpointer.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qsharedpointer_impl.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/QMutex \
		tmp/moc_predefs.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/bin/moc.exe
	D:\Softwares\Qt\5.14.2\mingw73_64\bin\moc.exe $(DEFINES) --include D:/VMwareOS/ShareData/dky/xianshi/tmp/moc_predefs.h -ID:/Softwares/Qt/5.14.2/mingw73_64/mkspecs/win32-g++ -ID:/VMwareOS/ShareData/dky/xianshi -ID:/VMwareOS/ShareData/dky/xianshi/src -ID:/Softwares/Qt/5.14.2/mingw73_64/include -ID:/Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets -ID:/Softwares/Qt/5.14.2/mingw73_64/include/QtGui -ID:/Softwares/Qt/5.14.2/mingw73_64/include/QtANGLE -ID:/Softwares/Qt/5.14.2/mingw73_64/include/QtNetwork -ID:/Softwares/Qt/5.14.2/mingw73_64/include/QtConcurrent -ID:/Softwares/Qt/5.14.2/mingw73_64/include/QtCore -ID:/Softwares/Qt/Tools/mingw730_64/lib/gcc/x86_64-w64-mingw32/7.3.0/include/c++ -ID:/Softwares/Qt/Tools/mingw730_64/lib/gcc/x86_64-w64-mingw32/7.3.0/include/c++/x86_64-w64-mingw32 -ID:/Softwares/Qt/Tools/mingw730_64/lib/gcc/x86_64-w64-mingw32/7.3.0/include/c++/backward -ID:/Softwares/Qt/Tools/mingw730_64/lib/gcc/x86_64-w64-mingw32/7.3.0/include -ID:/Softwares/Qt/Tools/mingw730_64/lib/gcc/x86_64-w64-mingw32/7.3.0/include-fixed -ID:/Softwares/Qt/Tools/mingw730_64/x86_64-w64-mingw32/include src\utils\PerformanceMonitor.h -o tmp\moc_PerformanceMonitor.cpp

tmp/moc_TargetItem.cpp: src/graphics/TargetItem.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/QGraphicsItem \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/qgraphicsitem.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/qtwidgetsglobal.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qtguiglobal.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qglobal.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qconfig-bootstrapped.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qconfig.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qtcore-config.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qsystemdetection.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qprocessordetection.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qcompilerdetection.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qtypeinfo.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qsysinfo.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qlogging.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qflags.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qatomic.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qbasicatomic.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qatomic_bootstrap.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qgenericatomic.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qatomic_cxx11.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qatomic_msvc.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qglobalstatic.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qmutex.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qnumeric.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qversiontagging.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qtgui-config.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/qtwidgets-config.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qobject.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qobjectdefs.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qnamespace.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qobjectdefs_impl.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qstring.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qchar.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qbytearray.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qrefcount.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qarraydata.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qstringliteral.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qstringalgorithms.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qstringview.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qstringbuilder.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qlist.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qalgorithms.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qiterator.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qhashfunctions.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qpair.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qvector.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qcontainertools_impl.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qpoint.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qbytearraylist.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qstringlist.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qregexp.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qstringmatcher.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qcoreevent.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qscopedpointer.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qmetatype.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qvarlengtharray.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qcontainerfwd.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qobject_impl.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qvariant.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qmap.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qdebug.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qhash.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qtextstream.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qiodevice.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qlocale.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qshareddata.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qset.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qcontiguouscache.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qsharedpointer.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qsharedpointer_impl.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qrect.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qmargins.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qsize.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qpainterpath.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qmatrix.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qpolygon.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qregion.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qwindowdefs.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qwindowdefs_win.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qdatastream.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qline.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qpixmap.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qpaintdevice.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qcolor.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qrgb.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qrgba64.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qimage.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qpixelformat.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qtransform.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/QGraphicsPixmapItem \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/QPropertyAnimation \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qpropertyanimation.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qvariantanimation.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qeasingcurve.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qabstractanimation.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/QPainter \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qpainter.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qtextoption.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qpen.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qbrush.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qfontinfo.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qfont.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qfontmetrics.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/QStyleOptionGraphicsItem \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/qstyleoption.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/qabstractspinbox.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/qwidget.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qpalette.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/qsizepolicy.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qcursor.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qkeysequence.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qevent.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qurl.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qurlquery.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qfile.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qfiledevice.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qvector2d.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qtouchdevice.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qvalidator.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qregularexpression.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qicon.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/qslider.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/qabstractslider.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/qstyle.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/qtabbar.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/qtabwidget.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/qrubberband.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/qframe.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qabstractitemmodel.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/QGraphicsSceneMouseEvent \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/qgraphicssceneevent.h \
		src/common/Target.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/QString \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/QPointF \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/QDateTime \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qdatetime.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/QJsonObject \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qjsonobject.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qjsonvalue.h \
		tmp/moc_predefs.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/bin/moc.exe
	D:\Softwares\Qt\5.14.2\mingw73_64\bin\moc.exe $(DEFINES) --include D:/VMwareOS/ShareData/dky/xianshi/tmp/moc_predefs.h -ID:/Softwares/Qt/5.14.2/mingw73_64/mkspecs/win32-g++ -ID:/VMwareOS/ShareData/dky/xianshi -ID:/VMwareOS/ShareData/dky/xianshi/src -ID:/Softwares/Qt/5.14.2/mingw73_64/include -ID:/Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets -ID:/Softwares/Qt/5.14.2/mingw73_64/include/QtGui -ID:/Softwares/Qt/5.14.2/mingw73_64/include/QtANGLE -ID:/Softwares/Qt/5.14.2/mingw73_64/include/QtNetwork -ID:/Softwares/Qt/5.14.2/mingw73_64/include/QtConcurrent -ID:/Softwares/Qt/5.14.2/mingw73_64/include/QtCore -ID:/Softwares/Qt/Tools/mingw730_64/lib/gcc/x86_64-w64-mingw32/7.3.0/include/c++ -ID:/Softwares/Qt/Tools/mingw730_64/lib/gcc/x86_64-w64-mingw32/7.3.0/include/c++/x86_64-w64-mingw32 -ID:/Softwares/Qt/Tools/mingw730_64/lib/gcc/x86_64-w64-mingw32/7.3.0/include/c++/backward -ID:/Softwares/Qt/Tools/mingw730_64/lib/gcc/x86_64-w64-mingw32/7.3.0/include -ID:/Softwares/Qt/Tools/mingw730_64/lib/gcc/x86_64-w64-mingw32/7.3.0/include-fixed -ID:/Softwares/Qt/Tools/mingw730_64/x86_64-w64-mingw32/include src\graphics\TargetItem.h -o tmp\moc_TargetItem.cpp

compiler_moc_objc_header_make_all:
compiler_moc_objc_header_clean:
compiler_moc_source_make_all:
compiler_moc_source_clean:
compiler_uic_make_all:
compiler_uic_clean:
compiler_yacc_decl_make_all:
compiler_yacc_decl_clean:
compiler_yacc_impl_make_all:
compiler_yacc_impl_clean:
compiler_lex_make_all:
compiler_lex_clean:
compiler_clean: compiler_rcc_clean compiler_moc_predefs_clean compiler_moc_header_clean 



####### Compile

tmp/main.o: src/main.cpp ../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/QApplication \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/qapplication.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/qtwidgetsglobal.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qtguiglobal.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qglobal.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qconfig-bootstrapped.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qconfig.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qtcore-config.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qsystemdetection.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qprocessordetection.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qcompilerdetection.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qtypeinfo.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qsysinfo.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qlogging.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qflags.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qatomic.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qbasicatomic.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qatomic_bootstrap.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qgenericatomic.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qatomic_cxx11.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qatomic_msvc.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qglobalstatic.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qmutex.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qnumeric.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qversiontagging.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qtgui-config.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/qtwidgets-config.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qcoreapplication.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qstring.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qchar.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qbytearray.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qrefcount.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qnamespace.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qarraydata.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qstringliteral.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qstringalgorithms.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qstringview.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qstringbuilder.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qobject.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qobjectdefs.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qobjectdefs_impl.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qlist.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qalgorithms.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qiterator.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qhashfunctions.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qpair.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qvector.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qcontainertools_impl.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qpoint.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qbytearraylist.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qstringlist.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qregexp.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qstringmatcher.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qcoreevent.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qscopedpointer.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qmetatype.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qvarlengtharray.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qcontainerfwd.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qobject_impl.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qeventloop.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qwindowdefs.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qwindowdefs_win.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qsize.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qmargins.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qcursor.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/qdesktopwidget.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/qwidget.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qpaintdevice.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qrect.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qpalette.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qcolor.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qrgb.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qrgba64.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qbrush.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qmatrix.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qpolygon.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qregion.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qdatastream.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qiodevice.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qline.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qtransform.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qpainterpath.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qimage.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qpixelformat.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qpixmap.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qsharedpointer.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qshareddata.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qhash.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qsharedpointer_impl.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qfont.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qfontmetrics.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qfontinfo.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/qsizepolicy.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qkeysequence.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qevent.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qvariant.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qmap.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qdebug.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qtextstream.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qlocale.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qset.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qcontiguouscache.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qurl.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qurlquery.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qfile.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qfiledevice.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qvector2d.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qtouchdevice.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qguiapplication.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qinputmethod.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/QDir \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qdir.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qfileinfo.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/QStandardPaths \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qstandardpaths.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/QLoggingCategory \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qloggingcategory.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/QDebug \
		src/MainWindow.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/QMainWindow \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/qmainwindow.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/qtabwidget.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qicon.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/QVBoxLayout \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/qboxlayout.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/qlayout.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/qlayoutitem.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/qgridlayout.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/QHBoxLayout \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/QMenuBar \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/qmenubar.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/qmenu.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/qaction.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/qactiongroup.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/QStatusBar \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/qstatusbar.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/QToolBar \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/qtoolbar.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/QAction \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/QLabel \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/qlabel.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/qframe.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/QProgressBar \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/qprogressbar.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/QSplitter \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/qsplitter.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/QDockWidget \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/qdockwidget.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/QTreeView \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/qtreeview.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/qabstractitemview.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/qabstractscrollarea.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qabstractitemmodel.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qitemselectionmodel.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/qabstractitemdelegate.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/qstyleoption.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/qabstractspinbox.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qvalidator.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qregularexpression.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/qslider.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/qabstractslider.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/qstyle.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/qtabbar.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/qrubberband.h \
		src/common/Constants.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/QString \
		src/utils/ConfigManager.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/QObject \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/QJsonObject \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qjsonobject.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qjsonvalue.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/QJsonDocument \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qjsondocument.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtNetwork/QHostAddress \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtNetwork/qhostaddress.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtNetwork/qtnetworkglobal.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtNetwork/qtnetwork-config.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtNetwork/qabstractsocket.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/QPointF
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o tmp\main.o src\main.cpp

tmp/MainWindow.o: src/MainWindow.cpp src/MainWindow.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/QMainWindow \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/qmainwindow.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/qtwidgetsglobal.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qtguiglobal.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qglobal.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qconfig-bootstrapped.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qconfig.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qtcore-config.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qsystemdetection.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qprocessordetection.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qcompilerdetection.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qtypeinfo.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qsysinfo.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qlogging.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qflags.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qatomic.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qbasicatomic.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qatomic_bootstrap.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qgenericatomic.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qatomic_cxx11.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qatomic_msvc.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qglobalstatic.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qmutex.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qnumeric.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qversiontagging.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qtgui-config.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/qtwidgets-config.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/qwidget.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qwindowdefs.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qobjectdefs.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qnamespace.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qobjectdefs_impl.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qwindowdefs_win.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qobject.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qstring.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qchar.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qbytearray.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qrefcount.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qarraydata.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qstringliteral.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qstringalgorithms.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qstringview.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qstringbuilder.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qlist.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qalgorithms.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qiterator.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qhashfunctions.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qpair.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qvector.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qcontainertools_impl.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qpoint.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qbytearraylist.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qstringlist.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qregexp.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qstringmatcher.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qcoreevent.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qscopedpointer.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qmetatype.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qvarlengtharray.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qcontainerfwd.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qobject_impl.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qmargins.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qpaintdevice.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qrect.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qsize.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qpalette.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qcolor.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qrgb.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qrgba64.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qbrush.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qmatrix.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qpolygon.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qregion.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qdatastream.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qiodevice.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qline.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qtransform.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qpainterpath.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qimage.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qpixelformat.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qpixmap.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qsharedpointer.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qshareddata.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qhash.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qsharedpointer_impl.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qfont.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qfontmetrics.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qfontinfo.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/qsizepolicy.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qcursor.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qkeysequence.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qevent.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qvariant.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qmap.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qdebug.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qtextstream.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qlocale.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qset.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qcontiguouscache.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qurl.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qurlquery.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qfile.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qfiledevice.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qvector2d.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qtouchdevice.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/qtabwidget.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qicon.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/QVBoxLayout \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/qboxlayout.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/qlayout.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/qlayoutitem.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/qgridlayout.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/QHBoxLayout \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/QMenuBar \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/qmenubar.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/qmenu.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/qaction.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/qactiongroup.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/QStatusBar \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/qstatusbar.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/QToolBar \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/qtoolbar.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/QAction \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/QLabel \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/qlabel.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/qframe.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/QProgressBar \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/qprogressbar.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/QSplitter \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/qsplitter.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/QDockWidget \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/qdockwidget.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/QTreeView \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/qtreeview.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/qabstractitemview.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/qabstractscrollarea.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qabstractitemmodel.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qitemselectionmodel.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/qabstractitemdelegate.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/qstyleoption.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/qabstractspinbox.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qvalidator.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qregularexpression.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/qslider.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/qabstractslider.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/qstyle.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/qtabbar.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/qrubberband.h \
		src/map/MapWidget.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/QGraphicsView \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/qgraphicsview.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qpainter.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qtextoption.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qpen.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/qscrollarea.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/qgraphicsscene.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/QGraphicsScene \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/QWheelEvent \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/QMouseEvent \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/QKeyEvent \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/QTimer \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qtimer.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qbasictimer.h \
		src/managers/TargetManager.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/QObject \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/QMap \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/QJsonObject \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qjsonobject.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qjsonvalue.h \
		src/common/Target.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/QString \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/QPointF \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/QDateTime \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qdatetime.h \
		src/network/UdpReceiver.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtNetwork/QUdpSocket \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtNetwork/qudpsocket.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtNetwork/qtnetworkglobal.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtNetwork/qtnetwork-config.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtNetwork/qabstractsocket.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtNetwork/qhostaddress.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtNetwork/QHostAddress \
		src/models/TargetModel.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/QAbstractTableModel \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/QList \
		src/utils/ConfigManager.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/QJsonDocument \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qjsondocument.h \
		src/common/Constants.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/QApplication \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/qapplication.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qcoreapplication.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qeventloop.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/qdesktopwidget.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qguiapplication.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qinputmethod.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/QCloseEvent \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/QMessageBox \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/qmessagebox.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/qdialog.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/QSettings \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qsettings.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/QFileDialog \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/qfiledialog.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qdir.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qfileinfo.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/QDesktopServices \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qdesktopservices.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qstandardpaths.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/QUrl
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o tmp\MainWindow.o src\MainWindow.cpp

tmp/MapWidget.o: src/map/MapWidget.cpp src/map/MapWidget.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/QGraphicsView \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/qgraphicsview.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/qtwidgetsglobal.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qtguiglobal.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qglobal.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qconfig-bootstrapped.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qconfig.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qtcore-config.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qsystemdetection.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qprocessordetection.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qcompilerdetection.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qtypeinfo.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qsysinfo.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qlogging.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qflags.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qatomic.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qbasicatomic.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qatomic_bootstrap.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qgenericatomic.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qatomic_cxx11.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qatomic_msvc.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qglobalstatic.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qmutex.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qnumeric.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qversiontagging.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qtgui-config.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/qtwidgets-config.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qmetatype.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qbytearray.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qrefcount.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qnamespace.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qarraydata.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qvarlengtharray.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qcontainerfwd.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qalgorithms.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qcontainertools_impl.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qhashfunctions.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qstring.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qchar.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qstringliteral.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qstringalgorithms.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qstringview.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qstringbuilder.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qpair.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qobjectdefs.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qobjectdefs_impl.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qpainter.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qrect.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qmargins.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qsize.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qpoint.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qscopedpointer.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qpixmap.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qpaintdevice.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qwindowdefs.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qwindowdefs_win.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qcolor.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qrgb.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qstringlist.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qlist.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qiterator.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qvector.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qbytearraylist.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qregexp.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qstringmatcher.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qrgba64.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qsharedpointer.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qshareddata.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qhash.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qsharedpointer_impl.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qobject.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qcoreevent.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qobject_impl.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qimage.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qpixelformat.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qtransform.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qmatrix.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qpolygon.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qregion.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qdatastream.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qiodevice.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qline.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qpainterpath.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qtextoption.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qpen.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qbrush.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qfontinfo.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qfont.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qfontmetrics.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/qscrollarea.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/qabstractscrollarea.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/qframe.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/qwidget.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qpalette.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/qsizepolicy.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qcursor.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qkeysequence.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qevent.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qvariant.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qmap.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qdebug.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qtextstream.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qlocale.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qset.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qcontiguouscache.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qurl.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qurlquery.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qfile.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qfiledevice.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qvector2d.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qtouchdevice.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/qgraphicsscene.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/QGraphicsScene \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/QWheelEvent \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/QMouseEvent \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/QKeyEvent \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/QTimer \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qtimer.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qbasictimer.h \
		src/map/TileLoader.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/QObject \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/QThreadPool \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qthreadpool.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qthread.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qrunnable.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/QRunnable \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/QPixmap \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/QRect \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/QSet \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/QMutex \
		src/utils/CoordinateConverter.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/QPointF \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/QRectF \
		src/graphics/TargetItem.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/QGraphicsItem \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/qgraphicsitem.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/QGraphicsPixmapItem \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/QPropertyAnimation \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qpropertyanimation.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qvariantanimation.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qeasingcurve.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qabstractanimation.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/QPainter \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/QStyleOptionGraphicsItem \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/qstyleoption.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/qabstractspinbox.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qvalidator.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qregularexpression.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qicon.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/qslider.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/qabstractslider.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/qstyle.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/qtabbar.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/qtabwidget.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/qrubberband.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qabstractitemmodel.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/QGraphicsSceneMouseEvent \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/qgraphicssceneevent.h \
		src/common/Target.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/QString \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/QDateTime \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qdatetime.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/QJsonObject \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qjsonobject.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qjsonvalue.h \
		src/common/Constants.h \
		src/utils/ConfigManager.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/QJsonDocument \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qjsondocument.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtNetwork/QHostAddress \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtNetwork/qhostaddress.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtNetwork/qtnetworkglobal.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtNetwork/qtnetwork-config.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtNetwork/qabstractsocket.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/QScrollBar \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/qscrollbar.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/QApplication \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/qapplication.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qcoreapplication.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qeventloop.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/qdesktopwidget.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qguiapplication.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qinputmethod.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/QDebug \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/QtMath \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qmath.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o tmp\MapWidget.o src\map\MapWidget.cpp

tmp/TileLoader.o: src/map/TileLoader.cpp src/map/TileLoader.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/QObject \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qobject.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qobjectdefs.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qnamespace.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qglobal.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qconfig-bootstrapped.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qconfig.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qtcore-config.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qsystemdetection.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qprocessordetection.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qcompilerdetection.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qtypeinfo.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qsysinfo.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qlogging.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qflags.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qatomic.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qbasicatomic.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qatomic_bootstrap.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qgenericatomic.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qatomic_cxx11.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qatomic_msvc.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qglobalstatic.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qmutex.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qnumeric.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qversiontagging.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qobjectdefs_impl.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qstring.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qchar.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qbytearray.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qrefcount.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qarraydata.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qstringliteral.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qstringalgorithms.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qstringview.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qstringbuilder.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qlist.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qalgorithms.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qiterator.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qhashfunctions.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qpair.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qvector.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qcontainertools_impl.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qpoint.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qbytearraylist.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qstringlist.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qregexp.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qstringmatcher.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qcoreevent.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qscopedpointer.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qmetatype.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qvarlengtharray.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qcontainerfwd.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qobject_impl.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/QThreadPool \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qthreadpool.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qthread.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qrunnable.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/QRunnable \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/QPixmap \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qpixmap.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qtguiglobal.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qtgui-config.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qpaintdevice.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qwindowdefs.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qwindowdefs_win.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qrect.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qmargins.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qsize.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qcolor.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qrgb.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qrgba64.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qsharedpointer.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qshareddata.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qhash.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qsharedpointer_impl.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qimage.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qpixelformat.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qtransform.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qmatrix.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qpolygon.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qregion.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qdatastream.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qiodevice.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qline.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qpainterpath.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/QRect \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/QSet \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qset.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/QMutex \
		src/map/TileCache.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/QCache \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qcache.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/QTimer \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qtimer.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qbasictimer.h \
		src/common/Constants.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/QString \
		src/utils/CoordinateConverter.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/QPointF \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/QRectF \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/QDir \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qdir.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qfileinfo.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qfile.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qfiledevice.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/QDebug \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qdebug.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qmap.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qtextstream.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qlocale.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qvariant.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qcontiguouscache.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/QCoreApplication \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qcoreapplication.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qeventloop.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o tmp\TileLoader.o src\map\TileLoader.cpp

tmp/TileCache.o: src/map/TileCache.cpp src/map/TileCache.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/QObject \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qobject.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qobjectdefs.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qnamespace.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qglobal.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qconfig-bootstrapped.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qconfig.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qtcore-config.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qsystemdetection.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qprocessordetection.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qcompilerdetection.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qtypeinfo.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qsysinfo.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qlogging.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qflags.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qatomic.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qbasicatomic.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qatomic_bootstrap.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qgenericatomic.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qatomic_cxx11.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qatomic_msvc.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qglobalstatic.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qmutex.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qnumeric.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qversiontagging.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qobjectdefs_impl.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qstring.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qchar.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qbytearray.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qrefcount.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qarraydata.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qstringliteral.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qstringalgorithms.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qstringview.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qstringbuilder.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qlist.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qalgorithms.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qiterator.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qhashfunctions.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qpair.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qvector.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qcontainertools_impl.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qpoint.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qbytearraylist.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qstringlist.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qregexp.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qstringmatcher.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qcoreevent.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qscopedpointer.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qmetatype.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qvarlengtharray.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qcontainerfwd.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qobject_impl.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/QPixmap \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qpixmap.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qtguiglobal.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qtgui-config.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qpaintdevice.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qwindowdefs.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qwindowdefs_win.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qrect.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qmargins.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qsize.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qcolor.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qrgb.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qrgba64.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qsharedpointer.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qshareddata.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qhash.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qsharedpointer_impl.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qimage.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qpixelformat.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qtransform.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qmatrix.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qpolygon.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qregion.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qdatastream.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qiodevice.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qline.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qpainterpath.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/QCache \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qcache.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/QMutex \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/QTimer \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qtimer.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qbasictimer.h \
		src/common/Constants.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/QString \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/QDebug \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qdebug.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qmap.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qtextstream.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qlocale.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qvariant.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qset.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qcontiguouscache.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o tmp\TileCache.o src\map\TileCache.cpp

tmp/UdpReceiver.o: src/network/UdpReceiver.cpp src/network/UdpReceiver.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/QObject \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qobject.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qobjectdefs.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qnamespace.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qglobal.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qconfig-bootstrapped.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qconfig.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qtcore-config.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qsystemdetection.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qprocessordetection.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qcompilerdetection.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qtypeinfo.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qsysinfo.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qlogging.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qflags.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qatomic.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qbasicatomic.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qatomic_bootstrap.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qgenericatomic.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qatomic_cxx11.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qatomic_msvc.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qglobalstatic.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qmutex.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qnumeric.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qversiontagging.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qobjectdefs_impl.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qstring.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qchar.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qbytearray.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qrefcount.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qarraydata.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qstringliteral.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qstringalgorithms.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qstringview.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qstringbuilder.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qlist.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qalgorithms.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qiterator.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qhashfunctions.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qpair.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qvector.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qcontainertools_impl.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qpoint.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qbytearraylist.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qstringlist.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qregexp.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qstringmatcher.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qcoreevent.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qscopedpointer.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qmetatype.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qvarlengtharray.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qcontainerfwd.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qobject_impl.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtNetwork/QUdpSocket \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtNetwork/qudpsocket.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtNetwork/qtnetworkglobal.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtNetwork/qtnetwork-config.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtNetwork/qabstractsocket.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qiodevice.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qdebug.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qhash.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qmap.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qtextstream.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qlocale.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qvariant.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qshareddata.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qset.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qcontiguouscache.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qsharedpointer.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qsharedpointer_impl.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtNetwork/qhostaddress.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtNetwork/QHostAddress \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/QTimer \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qtimer.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qbasictimer.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/QJsonObject \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qjsonobject.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qjsonvalue.h \
		src/network/IMessageParser.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/QByteArray \
		src/network/MessageParser.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/QJsonDocument \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qjsondocument.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/QJsonParseError \
		src/utils/ConfigManager.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/QPointF \
		src/common/Constants.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/QString \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtNetwork/QNetworkInterface \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtNetwork/qnetworkinterface.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/QDebug
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o tmp\UdpReceiver.o src\network\UdpReceiver.cpp

tmp/MessageParser.o: src/network/MessageParser.cpp src/network/MessageParser.h \
		src/network/IMessageParser.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/QJsonObject \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qjsonobject.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qjsonvalue.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qglobal.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qconfig-bootstrapped.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qconfig.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qtcore-config.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qsystemdetection.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qprocessordetection.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qcompilerdetection.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qtypeinfo.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qsysinfo.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qlogging.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qflags.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qatomic.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qbasicatomic.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qatomic_bootstrap.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qgenericatomic.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qatomic_cxx11.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qatomic_msvc.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qglobalstatic.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qmutex.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qnumeric.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qversiontagging.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qstring.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qchar.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qbytearray.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qrefcount.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qnamespace.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qarraydata.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qstringliteral.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qstringalgorithms.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qstringview.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qstringbuilder.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qiterator.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qpair.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/QByteArray \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/QJsonDocument \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qjsondocument.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/QJsonParseError \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/QJsonArray \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qjsonarray.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/QDebug \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qdebug.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qalgorithms.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qhash.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qlist.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qhashfunctions.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qvector.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qcontainertools_impl.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qpoint.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qbytearraylist.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qstringlist.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qregexp.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qstringmatcher.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qmap.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qtextstream.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qiodevice.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qobject.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qobjectdefs.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qobjectdefs_impl.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qcoreevent.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qscopedpointer.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qmetatype.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qvarlengtharray.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qcontainerfwd.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qobject_impl.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qlocale.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qvariant.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qshareddata.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qset.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qcontiguouscache.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qsharedpointer.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qsharedpointer_impl.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/QDateTime \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qdatetime.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/QDataStream \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qdatastream.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o tmp\MessageParser.o src\network\MessageParser.cpp

tmp/TargetModel.o: src/models/TargetModel.cpp src/models/TargetModel.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/QAbstractTableModel \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qabstractitemmodel.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qvariant.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qatomic.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qglobal.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qconfig-bootstrapped.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qconfig.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qtcore-config.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qsystemdetection.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qprocessordetection.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qcompilerdetection.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qtypeinfo.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qsysinfo.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qlogging.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qflags.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qglobalstatic.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qmutex.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qnumeric.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qversiontagging.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qbasicatomic.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qatomic_bootstrap.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qgenericatomic.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qatomic_cxx11.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qatomic_msvc.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qbytearray.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qrefcount.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qnamespace.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qarraydata.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qlist.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qalgorithms.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qiterator.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qhashfunctions.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qstring.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qchar.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qstringliteral.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qstringalgorithms.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qstringview.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qstringbuilder.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qpair.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qvector.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qcontainertools_impl.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qpoint.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qbytearraylist.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qstringlist.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qregexp.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qstringmatcher.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qmetatype.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qvarlengtharray.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qcontainerfwd.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qobjectdefs.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qobjectdefs_impl.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qmap.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qdebug.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qhash.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qtextstream.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qiodevice.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qobject.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qcoreevent.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qscopedpointer.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qobject_impl.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qlocale.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qshareddata.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qset.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qcontiguouscache.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qsharedpointer.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qsharedpointer_impl.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/QList \
		src/common/Target.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/QString \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/QPointF \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/QDateTime \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qdatetime.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/QJsonObject \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qjsonobject.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qjsonvalue.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/Qt \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/QIcon \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qicon.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qtguiglobal.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qtgui-config.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qsize.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qmargins.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qpixmap.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qpaintdevice.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qwindowdefs.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qwindowdefs_win.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qrect.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qcolor.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qrgb.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qrgba64.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qimage.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qpixelformat.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qtransform.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qmatrix.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qpolygon.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qregion.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qdatastream.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qline.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qpainterpath.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/QColor \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/QDebug
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o tmp\TargetModel.o src\models\TargetModel.cpp

tmp/TargetManager.o: src/managers/TargetManager.cpp src/managers/TargetManager.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/QObject \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qobject.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qobjectdefs.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qnamespace.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qglobal.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qconfig-bootstrapped.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qconfig.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qtcore-config.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qsystemdetection.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qprocessordetection.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qcompilerdetection.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qtypeinfo.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qsysinfo.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qlogging.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qflags.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qatomic.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qbasicatomic.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qatomic_bootstrap.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qgenericatomic.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qatomic_cxx11.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qatomic_msvc.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qglobalstatic.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qmutex.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qnumeric.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qversiontagging.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qobjectdefs_impl.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qstring.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qchar.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qbytearray.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qrefcount.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qarraydata.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qstringliteral.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qstringalgorithms.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qstringview.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qstringbuilder.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qlist.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qalgorithms.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qiterator.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qhashfunctions.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qpair.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qvector.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qcontainertools_impl.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qpoint.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qbytearraylist.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qstringlist.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qregexp.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qstringmatcher.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qcoreevent.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qscopedpointer.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qmetatype.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qvarlengtharray.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qcontainerfwd.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qobject_impl.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/QMap \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qmap.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qdebug.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qhash.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qtextstream.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qiodevice.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qlocale.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qvariant.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qshareddata.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qset.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qcontiguouscache.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qsharedpointer.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qsharedpointer_impl.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/QTimer \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qtimer.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qbasictimer.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/QJsonObject \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qjsonobject.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qjsonvalue.h \
		src/common/Target.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/QString \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/QPointF \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/QDateTime \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qdatetime.h \
		src/models/TargetModel.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/QAbstractTableModel \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qabstractitemmodel.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/QList \
		src/common/Constants.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/QDebug
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o tmp\TargetManager.o src\managers\TargetManager.cpp

tmp/CoordinateConverter.o: src/utils/CoordinateConverter.cpp src/utils/CoordinateConverter.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/QPointF \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qpoint.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qnamespace.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qglobal.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qconfig-bootstrapped.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qconfig.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qtcore-config.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qsystemdetection.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qprocessordetection.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qcompilerdetection.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qtypeinfo.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qsysinfo.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qlogging.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qflags.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qatomic.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qbasicatomic.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qatomic_bootstrap.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qgenericatomic.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qatomic_cxx11.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qatomic_msvc.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qglobalstatic.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qmutex.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qnumeric.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qversiontagging.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/QRectF \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qrect.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qmargins.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qsize.h \
		src/common/Constants.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/QString \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qstring.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qchar.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qbytearray.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qrefcount.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qarraydata.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qstringliteral.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qstringalgorithms.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qstringview.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qstringbuilder.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/QtMath \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qmath.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qalgorithms.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/QRect
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o tmp\CoordinateConverter.o src\utils\CoordinateConverter.cpp

tmp/ConfigManager.o: src/utils/ConfigManager.cpp src/utils/ConfigManager.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/QObject \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qobject.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qobjectdefs.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qnamespace.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qglobal.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qconfig-bootstrapped.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qconfig.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qtcore-config.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qsystemdetection.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qprocessordetection.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qcompilerdetection.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qtypeinfo.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qsysinfo.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qlogging.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qflags.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qatomic.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qbasicatomic.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qatomic_bootstrap.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qgenericatomic.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qatomic_cxx11.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qatomic_msvc.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qglobalstatic.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qmutex.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qnumeric.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qversiontagging.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qobjectdefs_impl.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qstring.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qchar.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qbytearray.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qrefcount.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qarraydata.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qstringliteral.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qstringalgorithms.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qstringview.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qstringbuilder.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qlist.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qalgorithms.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qiterator.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qhashfunctions.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qpair.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qvector.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qcontainertools_impl.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qpoint.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qbytearraylist.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qstringlist.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qregexp.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qstringmatcher.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qcoreevent.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qscopedpointer.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qmetatype.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qvarlengtharray.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qcontainerfwd.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qobject_impl.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/QJsonObject \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qjsonobject.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qjsonvalue.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/QJsonDocument \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qjsondocument.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtNetwork/QHostAddress \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtNetwork/qhostaddress.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtNetwork/qtnetworkglobal.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtNetwork/qtnetwork-config.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qshareddata.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qhash.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtNetwork/qabstractsocket.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qiodevice.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qdebug.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qmap.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qtextstream.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qlocale.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qvariant.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qset.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qcontiguouscache.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qsharedpointer.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qsharedpointer_impl.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/QPointF \
		src/common/Constants.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/QString \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/QFile \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qfile.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qfiledevice.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/QDir \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qdir.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qfileinfo.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/QJsonArray \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qjsonarray.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/QDebug \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/QStandardPaths \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qstandardpaths.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o tmp\ConfigManager.o src\utils\ConfigManager.cpp

tmp/PerformanceMonitor.o: src/utils/PerformanceMonitor.cpp src/utils/PerformanceMonitor.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/QObject \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qobject.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qobjectdefs.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qnamespace.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qglobal.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qconfig-bootstrapped.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qconfig.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qtcore-config.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qsystemdetection.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qprocessordetection.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qcompilerdetection.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qtypeinfo.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qsysinfo.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qlogging.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qflags.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qatomic.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qbasicatomic.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qatomic_bootstrap.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qgenericatomic.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qatomic_cxx11.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qatomic_msvc.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qglobalstatic.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qmutex.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qnumeric.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qversiontagging.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qobjectdefs_impl.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qstring.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qchar.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qbytearray.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qrefcount.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qarraydata.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qstringliteral.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qstringalgorithms.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qstringview.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qstringbuilder.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qlist.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qalgorithms.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qiterator.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qhashfunctions.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qpair.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qvector.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qcontainertools_impl.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qpoint.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qbytearraylist.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qstringlist.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qregexp.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qstringmatcher.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qcoreevent.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qscopedpointer.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qmetatype.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qvarlengtharray.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qcontainerfwd.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qobject_impl.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/QTimer \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qtimer.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qbasictimer.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/QElapsedTimer \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qelapsedtimer.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/QMap \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qmap.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qdebug.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qhash.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qtextstream.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qiodevice.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qlocale.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qvariant.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qshareddata.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qset.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qcontiguouscache.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qsharedpointer.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qsharedpointer_impl.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/QMutex \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/QApplication \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/qapplication.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/qtwidgetsglobal.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qtguiglobal.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qtgui-config.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/qtwidgets-config.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qcoreapplication.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qeventloop.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qwindowdefs.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qwindowdefs_win.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qsize.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qmargins.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qcursor.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/qdesktopwidget.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/qwidget.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qpaintdevice.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qrect.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qpalette.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qcolor.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qrgb.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qrgba64.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qbrush.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qmatrix.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qpolygon.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qregion.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qdatastream.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qline.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qtransform.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qpainterpath.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qimage.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qpixelformat.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qpixmap.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qfont.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qfontmetrics.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qfontinfo.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/qsizepolicy.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qkeysequence.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qevent.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qurl.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qurlquery.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qfile.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qfiledevice.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qvector2d.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qtouchdevice.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qguiapplication.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qinputmethod.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/QDebug \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/QProcess \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qprocess.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/QDateTime \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qdatetime.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o tmp\PerformanceMonitor.o src\utils\PerformanceMonitor.cpp

tmp/TargetItem.o: src/graphics/TargetItem.cpp src/graphics/TargetItem.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/QGraphicsItem \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/qgraphicsitem.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/qtwidgetsglobal.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qtguiglobal.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qglobal.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qconfig-bootstrapped.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qconfig.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qtcore-config.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qsystemdetection.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qprocessordetection.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qcompilerdetection.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qtypeinfo.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qsysinfo.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qlogging.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qflags.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qatomic.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qbasicatomic.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qatomic_bootstrap.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qgenericatomic.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qatomic_cxx11.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qatomic_msvc.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qglobalstatic.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qmutex.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qnumeric.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qversiontagging.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qtgui-config.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/qtwidgets-config.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qobject.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qobjectdefs.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qnamespace.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qobjectdefs_impl.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qstring.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qchar.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qbytearray.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qrefcount.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qarraydata.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qstringliteral.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qstringalgorithms.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qstringview.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qstringbuilder.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qlist.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qalgorithms.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qiterator.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qhashfunctions.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qpair.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qvector.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qcontainertools_impl.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qpoint.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qbytearraylist.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qstringlist.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qregexp.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qstringmatcher.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qcoreevent.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qscopedpointer.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qmetatype.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qvarlengtharray.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qcontainerfwd.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qobject_impl.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qvariant.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qmap.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qdebug.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qhash.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qtextstream.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qiodevice.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qlocale.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qshareddata.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qset.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qcontiguouscache.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qsharedpointer.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qsharedpointer_impl.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qrect.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qmargins.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qsize.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qpainterpath.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qmatrix.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qpolygon.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qregion.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qwindowdefs.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qwindowdefs_win.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qdatastream.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qline.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qpixmap.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qpaintdevice.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qcolor.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qrgb.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qrgba64.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qimage.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qpixelformat.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qtransform.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/QGraphicsPixmapItem \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/QPropertyAnimation \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qpropertyanimation.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qvariantanimation.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qeasingcurve.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qabstractanimation.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/QPainter \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qpainter.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qtextoption.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qpen.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qbrush.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qfontinfo.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qfont.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qfontmetrics.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/QStyleOptionGraphicsItem \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/qstyleoption.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/qabstractspinbox.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/qwidget.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qpalette.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/qsizepolicy.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qcursor.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qkeysequence.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qevent.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qurl.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qurlquery.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qfile.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qfiledevice.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qvector2d.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qtouchdevice.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qvalidator.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qregularexpression.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtGui/qicon.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/qslider.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/qabstractslider.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/qstyle.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/qtabbar.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/qtabwidget.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/qrubberband.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/qframe.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qabstractitemmodel.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/QGraphicsSceneMouseEvent \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/qgraphicssceneevent.h \
		src/common/Target.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/QString \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/QPointF \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/QDateTime \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qdatetime.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/QJsonObject \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qjsonobject.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qjsonvalue.h \
		src/common/Constants.h \
		src/utils/ConfigManager.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/QObject \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/QJsonDocument \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/qjsondocument.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtNetwork/QHostAddress \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtNetwork/qhostaddress.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtNetwork/qtnetworkglobal.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtNetwork/qtnetwork-config.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtNetwork/qabstractsocket.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/QGraphicsScene \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtWidgets/qgraphicsscene.h \
		../../../../Softwares/Qt/5.14.2/mingw73_64/include/QtCore/QDebug
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o tmp\TargetItem.o src\graphics\TargetItem.cpp

tmp/qrc_resources.o: tmp/qrc_resources.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o tmp\qrc_resources.o tmp\qrc_resources.cpp

tmp/moc_MainWindow.o: tmp/moc_MainWindow.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o tmp\moc_MainWindow.o tmp\moc_MainWindow.cpp

tmp/moc_MapWidget.o: tmp/moc_MapWidget.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o tmp\moc_MapWidget.o tmp\moc_MapWidget.cpp

tmp/moc_TileLoader.o: tmp/moc_TileLoader.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o tmp\moc_TileLoader.o tmp\moc_TileLoader.cpp

tmp/moc_TileCache.o: tmp/moc_TileCache.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o tmp\moc_TileCache.o tmp\moc_TileCache.cpp

tmp/moc_UdpReceiver.o: tmp/moc_UdpReceiver.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o tmp\moc_UdpReceiver.o tmp\moc_UdpReceiver.cpp

tmp/moc_TargetModel.o: tmp/moc_TargetModel.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o tmp\moc_TargetModel.o tmp\moc_TargetModel.cpp

tmp/moc_TargetManager.o: tmp/moc_TargetManager.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o tmp\moc_TargetManager.o tmp\moc_TargetManager.cpp

tmp/moc_ConfigManager.o: tmp/moc_ConfigManager.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o tmp\moc_ConfigManager.o tmp\moc_ConfigManager.cpp

tmp/moc_PerformanceMonitor.o: tmp/moc_PerformanceMonitor.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o tmp\moc_PerformanceMonitor.o tmp\moc_PerformanceMonitor.cpp

tmp/moc_TargetItem.o: tmp/moc_TargetItem.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o tmp\moc_TargetItem.o tmp\moc_TargetItem.cpp

####### Install

install:  FORCE

uninstall:  FORCE

FORCE:

