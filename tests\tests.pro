QT += testlib core
QT -= gui

CONFIG += qt console warn_on depend_includepath testcase
CONFIG -= app_bundle
CONFIG += c++17

TEMPLATE = app

# 测试目标名称
TARGET = QMapAppTests

# 包含主项目的源文件路径
INCLUDEPATH += ../src

# 测试源文件
SOURCES += \
    test_coordinate_converter.cpp

# 需要测试的主项目源文件
SOURCES += \
    ../src/utils/CoordinateConverter.cpp \
    ../src/common/Target.cpp

HEADERS += \
    ../src/utils/CoordinateConverter.h \
    ../src/common/Target.h \
    ../src/common/Constants.h

# 输出目录
DESTDIR = ../build/tests
OBJECTS_DIR = ../build/tests/obj
MOC_DIR = ../build/tests/moc

# 编译器配置
win32 {
    QMAKE_CXXFLAGS += /std:c++17
}
unix {
    QMAKE_CXXFLAGS += -std=c++17
}
