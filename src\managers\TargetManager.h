#ifndef TARGETMANAGER_H
#define TARGETMANAGER_H

#include <QObject>
#include <QMap>
#include <QTimer>
#include <QJsonObject>
#include "common/Target.h"

class TargetModel;

/**
 * @brief 目标管理器
 * 
 * 负责管理所有目标对象的生命周期、状态更新和数据同步
 */
class TargetManager : public QObject
{
    Q_OBJECT

public:
    explicit TargetManager(QObject *parent = nullptr);
    ~TargetManager();
    
    // 目标操作
    void addTarget(const Target& target);
    void updateTarget(const QString& targetId, const QJsonObject& data);
    void removeTarget(const QString& targetId);
    void clearAllTargets();
    
    // 目标查询
    Target* getTarget(const QString& targetId) const;
    QList<Target*> getAllTargets() const;
    QStringList getTargetIds() const;
    int getTargetCount() const;
    
    // 模型访问
    TargetModel* model() const;
    
    // 配置
    void setTargetTimeout(int seconds);
    int getTargetTimeout() const;
    void setAutoCleanup(bool enabled);
    bool isAutoCleanupEnabled() const;

signals:
    void targetAdded(const QString& targetId);
    void targetUpdated(const QString& targetId);
    void targetRemoved(const QString& targetId);
    void targetTimeout(const QString& targetId);
    void targetCountChanged(int count);

public slots:
    void checkTargetTimeouts();
    void onTargetDataReceived(const QString& targetId, const QJsonObject& data);

private:
    void updateTargetFromJson(Target* target, const QJsonObject& data);
    void emitTargetUpdated(const QString& targetId);
    void startTimeoutTimer();
    void stopTimeoutTimer();

private:
    QMap<QString, Target*> m_targets;
    TargetModel* m_model;
    QTimer* m_timeoutTimer;
    
    // 配置
    int m_targetTimeoutSeconds;
    bool m_autoCleanupEnabled;
    
    // 常量
    static const int DEFAULT_TIMEOUT_SECONDS;
    static const int TIMEOUT_CHECK_INTERVAL;
};

#endif // TARGETMANAGER_H
