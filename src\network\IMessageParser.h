#ifndef IMESSAGEPARSER_H
#define IMESSAGEPARSER_H

#include <QJsonObject>
#include <QByteArray>

/**
 * @brief 消息解析器接口
 * 
 * 定义消息解析的统一接口，支持不同的消息格式
 */
class IMessageParser
{
public:
    virtual ~IMessageParser() = default;
    
    /**
     * @brief 解析消息数据
     * @param data 原始消息数据
     * @return 解析后的JSON对象，解析失败返回空对象
     */
    virtual QJsonObject parseMessage(const QByteArray& data) = 0;
    
    /**
     * @brief 验证消息格式
     * @param data 原始消息数据
     * @return 如果数据格式有效返回true
     */
    virtual bool isValidMessage(const QByteArray& data) = 0;
    
    /**
     * @brief 获取解析器名称
     * @return 解析器名称
     */
    virtual QString parserName() const = 0;
    
    /**
     * @brief 获取支持的消息类型
     * @return 支持的消息类型列表
     */
    virtual QStringList supportedTypes() const = 0;
};

#endif // IMESSAGEPARSER_H
