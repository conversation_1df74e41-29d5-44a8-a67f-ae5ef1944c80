#ifndef TARGETITEM_H
#define TARGETITEM_H

#include <QGraphicsItem>
#include <QGraphicsPixmapItem>
#include <QPropertyAnimation>
#include <QPainter>
#include <QStyleOptionGraphicsItem>
#include <QGraphicsSceneMouseEvent>
#include "common/Target.h"

/**
 * @brief 目标图形项
 * 
 * 在地图上显示目标的图形项，支持动画和交互
 */
class TargetItem : public QObject, public QGraphicsPixmapItem
{
    Q_OBJECT
    Q_PROPERTY(QPointF position READ pos WRITE setPos)
    Q_PROPERTY(qreal rotation READ rotation WRITE setRotation)
    Q_PROPERTY(qreal opacity READ opacity WRITE setOpacity)

public:
    explicit TargetItem(const Target& target, QGraphicsItem *parent = nullptr);
    ~TargetItem();
    
    // 目标数据
    void updateTarget(const Target& target);
    const Target& target() const;
    QString targetId() const;
    
    // 外观设置
    void setIcon(const QPixmap& icon);
    void setIconSize(const QSize& size);
    void setShowLabel(bool show);
    void setShowTrail(bool show);
    void setHighlighted(bool highlighted);
    
    // 动画控制
    void animateToPosition(const QPointF& newPos, int duration = 1000);
    void animateRotation(qreal newRotation, int duration = 500);
    void animateFadeIn(int duration = 500);
    void animateFadeOut(int duration = 500);
    
    // QGraphicsItem接口
    QRectF boundingRect() const override;
    void paint(QPainter *painter, const QStyleOptionGraphicsItem *option, QWidget *widget) override;
    QPainterPath shape() const override;

signals:
    void clicked(const QString& targetId);
    void doubleClicked(const QString& targetId);
    void positionChanged(const QString& targetId, const QPointF& position);

protected:
    void mousePressEvent(QGraphicsSceneMouseEvent *event) override;
    void mouseDoubleClickEvent(QGraphicsSceneMouseEvent *event) override;
    void hoverEnterEvent(QGraphicsSceneHoverEvent *event) override;
    void hoverLeaveEvent(QGraphicsSceneHoverEvent *event) override;

private slots:
    void onPositionAnimationFinished();
    void onRotationAnimationFinished();

private:
    void setupAnimations();
    void updateAppearance();
    void drawLabel(QPainter *painter);
    void drawTrail(QPainter *painter);
    void drawSelectionIndicator(QPainter *painter);
    void addTrailPoint(const QPointF& point);

private:
    Target m_target;
    
    // 外观设置
    QPixmap m_icon;
    QSize m_iconSize;
    bool m_showLabel;
    bool m_showTrail;
    bool m_highlighted;
    bool m_hovered;
    
    // 动画
    QPropertyAnimation* m_positionAnimation;
    QPropertyAnimation* m_rotationAnimation;
    QPropertyAnimation* m_opacityAnimation;
    
    // 轨迹
    QList<QPointF> m_trailPoints;
    static const int MAX_TRAIL_POINTS;
    
    // 样式常量
    static const QColor LABEL_BACKGROUND_COLOR;
    static const QColor LABEL_TEXT_COLOR;
    static const QColor TRAIL_COLOR;
    static const QColor SELECTION_COLOR;
    static const int LABEL_MARGIN;
    static const int SELECTION_MARGIN;
};

#endif // TARGETITEM_H
