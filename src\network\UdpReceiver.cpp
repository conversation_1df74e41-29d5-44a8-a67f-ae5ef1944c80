#include "UdpReceiver.h"
#include "IMessageParser.h"
#include "MessageParser.h"
#include "utils/ConfigManager.h"
#include "common/Constants.h"
#include <QNetworkInterface>
#include <QDebug>

UdpReceiver::UdpReceiver(QObject *parent)
    : QObject(parent)
    , m_socket(nullptr)
    , m_parser(nullptr)
    , m_statisticsTimer(new QTimer(this))
    , m_totalPackets(0)
    , m_totalBytes(0)
    , m_validMessages(0)
    , m_isListening(false)
{
    setupSocket();
    
    // 创建默认的JSON解析器
    m_parser = new JsonMessageParser();
    
    // 设置统计定时器
    m_statisticsTimer->setInterval(5000); // 5秒更新一次统计
    connect(m_statisticsTimer, &QTimer::timeout, this, &UdpReceiver::updateStatistics);
    
    qDebug() << "UdpReceiver initialized";
}

UdpReceiver::~UdpReceiver()
{
    stopListening();
    delete m_parser;
}

void UdpReceiver::setupSocket()
{
    m_socket = new QUdpSocket(this);
    
    // 连接信号
    connect(m_socket, &QUdpSocket::readyRead, 
            this, &UdpReceiver::processPendingDatagrams);
    connect(m_socket, QOverload<QAbstractSocket::SocketError>::of(&QUdpSocket::error),
            this, &UdpReceiver::onSocketError);
    connect(m_socket, &QUdpSocket::stateChanged,
            this, &UdpReceiver::onSocketStateChanged);
}

bool UdpReceiver::startListening()
{
    if (m_isListening) {
        return true;
    }
    
    // 从配置加载组播组
    ConfigManager& config = ConfigManager::instance();
    QList<ConfigManager::MulticastGroup> groups = config.multicastGroups();
    
    if (groups.isEmpty()) {
        qWarning() << "No multicast groups configured";
        return false;
    }
    
    // 清除现有组播组
    clearMulticastGroups();
    
    // 加入配置的组播组
    bool success = false;
    for (const ConfigManager::MulticastGroup& group : groups) {
        if (group.enabled) {
            if (joinMulticastGroup(group.address, group.port)) {
                success = true;
            }
        }
    }
    
    if (success) {
        m_isListening = true;
        m_statisticsTimer->start();
        emitNetworkStatus();
        qInfo() << "UDP receiver started listening";
    }
    
    return success;
}

void UdpReceiver::stopListening()
{
    if (!m_isListening) {
        return;
    }
    
    m_statisticsTimer->stop();
    clearMulticastGroups();
    
    if (m_socket->state() != QAbstractSocket::UnconnectedState) {
        m_socket->close();
    }
    
    m_isListening = false;
    emitNetworkStatus();
    
    qInfo() << "UDP receiver stopped listening";
}

bool UdpReceiver::isListening() const
{
    return m_isListening;
}

bool UdpReceiver::joinMulticastGroup(const QHostAddress& address, quint16 port)
{
    // 检查是否已经加入该组
    for (const MulticastGroup& group : m_groups) {
        if (group.address == address && group.port == port) {
            return group.joined;
        }
    }
    
    // 绑定到指定端口（如果还没有绑定）
    if (m_socket->state() == QAbstractSocket::UnconnectedState) {
        if (!m_socket->bind(QHostAddress::AnyIPv4, port, QUdpSocket::ShareAddress)) {
            qWarning() << "Failed to bind to port" << port << ":" << m_socket->errorString();
            return false;
        }
    }
    
    // 加入组播组
    bool joined = m_socket->joinMulticastGroup(address);
    if (joined) {
        MulticastGroup group;
        group.address = address;
        group.port = port;
        group.joined = true;
        m_groups.append(group);
        
        qInfo() << "Joined multicast group:" << address.toString() << "port:" << port;
    } else {
        qWarning() << "Failed to join multicast group:" << address.toString() 
                   << "port:" << port << "error:" << m_socket->errorString();
    }
    
    return joined;
}

bool UdpReceiver::leaveMulticastGroup(const QHostAddress& address, quint16 port)
{
    for (int i = 0; i < m_groups.size(); ++i) {
        MulticastGroup& group = m_groups[i];
        if (group.address == address && group.port == port && group.joined) {
            bool left = m_socket->leaveMulticastGroup(address);
            if (left) {
                m_groups.removeAt(i);
                qInfo() << "Left multicast group:" << address.toString() << "port:" << port;
            } else {
                qWarning() << "Failed to leave multicast group:" << address.toString() 
                          << "port:" << port << "error:" << m_socket->errorString();
            }
            return left;
        }
    }
    
    return false;
}

void UdpReceiver::clearMulticastGroups()
{
    for (const MulticastGroup& group : m_groups) {
        if (group.joined) {
            m_socket->leaveMulticastGroup(group.address);
        }
    }
    m_groups.clear();
    
    qDebug() << "All multicast groups cleared";
}

void UdpReceiver::setMessageParser(IMessageParser* parser)
{
    if (m_parser != parser) {
        delete m_parser;
        m_parser = parser;
        qDebug() << "Message parser changed to:" << (parser ? parser->parserName() : "null");
    }
}

IMessageParser* UdpReceiver::messageParser() const
{
    return m_parser;
}

quint64 UdpReceiver::totalPacketsReceived() const
{
    return m_totalPackets;
}

quint64 UdpReceiver::totalBytesReceived() const
{
    return m_totalBytes;
}

quint64 UdpReceiver::validMessagesReceived() const
{
    return m_validMessages;
}

void UdpReceiver::processPendingDatagrams()
{
    while (m_socket->hasPendingDatagrams()) {
        QByteArray datagram;
        QHostAddress sender;
        quint16 senderPort;
        
        datagram.resize(m_socket->pendingDatagramSize());
        qint64 bytesRead = m_socket->readDatagram(datagram.data(), datagram.size(), 
                                                 &sender, &senderPort);
        
        if (bytesRead > 0) {
            m_totalPackets++;
            m_totalBytes += bytesRead;
            
            processMessage(datagram, sender, senderPort);
        }
    }
}

void UdpReceiver::onSocketError(QAbstractSocket::SocketError error)
{
    qWarning() << "UDP socket error:" << error << m_socket->errorString();
    emitNetworkStatus();
}

void UdpReceiver::onSocketStateChanged(QAbstractSocket::SocketState state)
{
    qDebug() << "UDP socket state changed to:" << state;
    emitNetworkStatus();
}

void UdpReceiver::updateStatistics()
{
    emit statisticsUpdated();
}

void UdpReceiver::processMessage(const QByteArray& data, const QHostAddress& sender, quint16 port)
{
    Q_UNUSED(sender)
    Q_UNUSED(port)
    
    if (!m_parser) {
        qWarning() << "No message parser available";
        return;
    }
    
    // 解析消息
    QJsonObject message = m_parser->parseMessage(data);
    if (message.isEmpty()) {
        qDebug() << "Failed to parse message from" << sender.toString() << ":" << port;
        return;
    }
    
    m_validMessages++;
    
    // 发射通用消息信号
    emit messageReceived(message);
    
    // 如果是目标数据，发射专用信号
    QString messageType = message["type"].toString();
    if (messageType == "target_update" || messageType == "target_remove") {
        QString targetId = message["id"].toString();
        if (!targetId.isEmpty()) {
            emit targetDataReceived(targetId, message);
        }
    }
    
    qDebug() << "Message processed:" << messageType << "from" << sender.toString();
}

void UdpReceiver::emitNetworkStatus()
{
    bool connected = m_isListening && m_socket->state() == QAbstractSocket::BoundState;
    emit networkStatusChanged(connected);
}
