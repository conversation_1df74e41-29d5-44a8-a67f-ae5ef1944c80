@echo off
REM QMapApp 构建脚本 (Windows)
REM 需要安装Qt 5.14或更高版本

echo ========================================
echo QMapApp 构建脚本
echo ========================================

REM 检查qmake是否可用
where qmake >nul 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo 错误: 找不到qmake命令
    echo 请确保Qt已正确安装并添加到PATH环境变量中
    echo 或者运行Qt命令提示符
    pause
    exit /b 1
)

REM 显示Qt版本
echo 检测到的Qt版本:
qmake -version

REM 创建构建目录
if not exist "build" mkdir build
cd build

echo.
echo 正在生成Makefile...
qmake ..\QMapApp.pro
if %ERRORLEVEL% NEQ 0 (
    echo 错误: qmake失败
    pause
    exit /b 1
)

echo.
echo 正在编译项目...
REM 尝试使用nmake (Visual Studio) 或 mingw32-make (MinGW)
where nmake >nul 2>nul
if %ERRORLEVEL% EQU 0 (
    echo 使用nmake编译...
    nmake
) else (
    where mingw32-make >nul 2>nul
    if %ERRORLEVEL% EQU 0 (
        echo 使用mingw32-make编译...
        mingw32-make
    ) else (
        where make >nul 2>nul
        if %ERRORLEVEL% EQU 0 (
            echo 使用make编译...
            make
        ) else (
            echo 错误: 找不到make工具
            echo 请确保Visual Studio或MinGW已正确安装
            pause
            exit /b 1
        )
    )
)

if %ERRORLEVEL% NEQ 0 (
    echo 错误: 编译失败
    pause
    exit /b 1
)

echo.
echo ========================================
echo 编译成功！
echo ========================================
echo 可执行文件位置: build\bin\QMapApp.exe
echo.

REM 检查是否需要生成测试瓦片
if not exist "..\map_tiles" (
    echo 检测到没有地图瓦片，是否生成测试瓦片？
    set /p generate_tiles="输入 y 生成测试瓦片，其他键跳过: "
    if /i "%generate_tiles%"=="y" (
        echo 正在生成测试瓦片...
        cd ..
        python generate_test_tiles.py
        cd build
    )
)

echo.
echo 要运行应用程序，请执行:
echo   cd build\bin
echo   QMapApp.exe
echo.
echo 要测试UDP功能，请在另一个终端运行:
echo   python test_udp_sender.py
echo.

pause
