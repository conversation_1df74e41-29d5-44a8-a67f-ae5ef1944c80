#ifndef CONFIGMANAGER_H
#define CONFIGMANAGER_H

#include <QObject>
#include <QJsonObject>
#include <QJsonDocument>
#include <QHostAddress>
#include <QPointF>

/**
 * @brief 配置管理器
 * 
 * 单例模式，负责应用程序配置的加载、保存和管理
 */
class ConfigManager : public QObject
{
    Q_OBJECT

public:
    static ConfigManager& instance();
    
    // 配置加载和保存
    bool loadConfig(const QString& configFile = QString());
    bool saveConfig(const QString& configFile = QString());
    
    // 网络配置
    struct MulticastGroup {
        QHostAddress address;
        quint16 port;
        bool enabled;
        QString name;
    };
    
    QList<MulticastGroup> multicastGroups() const;
    void setMulticastGroups(const QList<MulticastGroup>& groups);
    QString messageParser() const;
    void setMessageParser(const QString& parser);
    
    // 地图配置
    QString tileSource() const;
    void setTileSource(const QString& path);
    int cacheSizeMB() const;
    void setCacheSizeMB(int sizeMB);
    QPointF initialCenter() const;
    void setInitialCenter(const QPointF& center);
    int initialZoomLevel() const;
    void setInitialZoomLevel(int level);
    
    // 目标配置
    QString defaultIcon() const;
    void setDefaultIcon(const QString& iconPath);
    int animationDuration() const;
    void setAnimationDuration(int duration);
    
    // 应用配置
    bool autoStart() const;
    void setAutoStart(bool enabled);
    QString logLevel() const;
    void setLogLevel(const QString& level);

signals:
    void configChanged();

private:
    explicit ConfigManager(QObject *parent = nullptr);
    ~ConfigManager() = default;
    
    // 禁用拷贝构造和赋值
    ConfigManager(const ConfigManager&) = delete;
    ConfigManager& operator=(const ConfigManager&) = delete;
    
    void setDefaultValues();
    QJsonObject multicastGroupToJson(const MulticastGroup& group) const;
    MulticastGroup multicastGroupFromJson(const QJsonObject& json) const;

private:
    QJsonObject m_config;
    QString m_configFile;
    bool m_loaded;
};

#endif // CONFIGMANAGER_H
