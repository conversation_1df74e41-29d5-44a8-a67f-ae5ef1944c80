#ifndef TARGET_H
#define TARGET_H

#include <QString>
#include <QPointF>
#include <QDateTime>
#include <QJsonObject>

/**
 * @brief 目标数据结构
 * 
 * 表示地图上的一个目标对象，包含位置、状态、属性等信息
 */
struct Target {
    QString id;                    // 唯一标识符
    QString name;                  // 目标名称
    double latitude;               // 纬度(WGS84)
    double longitude;              // 经度(WGS84)
    double altitude;               // 高度(米)
    double heading;                // 朝向角度(度，0-360)
    double speed;                  // 速度(m/s)
    QString type;                  // 目标类型
    QString status;                // 目标状态
    QString iconPath;              // 图标路径
    QDateTime lastUpdate;          // 最后更新时间
    QJsonObject properties;        // 扩展属性
    
    // 构造函数
    Target() 
        : latitude(0.0)
        , longitude(0.0)
        , altitude(0.0)
        , heading(0.0)
        , speed(0.0)
        , lastUpdate(QDateTime::currentDateTime())
    {}
    
    Target(const QString& targetId, double lat, double lon)
        : id(targetId)
        , latitude(lat)
        , longitude(lon)
        , altitude(0.0)
        , heading(0.0)
        , speed(0.0)
        , lastUpdate(QDateTime::currentDateTime())
    {}
    
    // 便利方法
    QPointF position() const {
        return QPointF(longitude, latitude);
    }
    
    void setPosition(double lat, double lon) {
        latitude = lat;
        longitude = lon;
        lastUpdate = QDateTime::currentDateTime();
    }
    
    void setPosition(const QPointF& pos) {
        longitude = pos.x();
        latitude = pos.y();
        lastUpdate = QDateTime::currentDateTime();
    }
    
    bool isValid() const {
        return !id.isEmpty() && 
               latitude >= -90.0 && latitude <= 90.0 &&
               longitude >= -180.0 && longitude <= 180.0;
    }
    
    // 从JSON创建目标
    static Target fromJson(const QJsonObject& json) {
        Target target;
        target.id = json["id"].toString();
        target.name = json["name"].toString();
        target.latitude = json["latitude"].toDouble();
        target.longitude = json["longitude"].toDouble();
        target.altitude = json["altitude"].toDouble();
        target.heading = json["heading"].toDouble();
        target.speed = json["speed"].toDouble();
        target.type = json["type"].toString();
        target.status = json["status"].toString();
        target.iconPath = json["iconPath"].toString();
        target.properties = json["properties"].toObject();
        return target;
    }
    
    // 转换为JSON
    QJsonObject toJson() const {
        QJsonObject json;
        json["id"] = id;
        json["name"] = name;
        json["latitude"] = latitude;
        json["longitude"] = longitude;
        json["altitude"] = altitude;
        json["heading"] = heading;
        json["speed"] = speed;
        json["type"] = type;
        json["status"] = status;
        json["iconPath"] = iconPath;
        json["lastUpdate"] = lastUpdate.toString(Qt::ISODate);
        json["properties"] = properties;
        return json;
    }
};

#endif // TARGET_H
