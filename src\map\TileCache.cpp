#include "TileCache.h"
#include "common/Constants.h"
#include <QDebug>

// 常量定义
const int TileCache::DEFAULT_MAX_COST = Constants::DEFAULT_CACHE_SIZE_MB * 1024 * 1024; // 转换为字节
const int TileCache::STATISTICS_UPDATE_INTERVAL = 5000; // 5秒

TileCache::TileCache(QObject *parent)
    : QObject(parent)
    , m_cache(DEFAULT_MAX_COST)
    , m_statisticsTimer(new QTimer(this))
    , m_hitCount(0)
    , m_missCount(0)
{
    // 设置缓存成本计算函数
    m_cache.setMaxCost(DEFAULT_MAX_COST);
    
    // 设置统计更新定时器
    m_statisticsTimer->setInterval(STATISTICS_UPDATE_INTERVAL);
    connect(m_statisticsTimer, &QTimer::timeout, this, &TileCache::updateStatistics);
    m_statisticsTimer->start();
    
    qDebug() << "TileCache initialized with max cost:" << DEFAULT_MAX_COST << "bytes";
}

TileCache::~TileCache()
{
    clear();
}

void TileCache::insert(const QString& key, const QPixmap& pixmap)
{
    QMutexLocker locker(&m_mutex);
    
    // 计算像素图的内存成本（估算）
    int cost = pixmap.width() * pixmap.height() * pixmap.depth() / 8;
    
    // 创建像素图副本并插入缓存
    QPixmap* cachedPixmap = new QPixmap(pixmap);
    bool inserted = m_cache.insert(key, cachedPixmap, cost);
    
    if (!inserted) {
        delete cachedPixmap;
        qWarning() << "Failed to insert tile into cache:" << key;
    } else {
        qDebug() << "Tile cached:" << key << "cost:" << cost;
    }
}

QPixmap* TileCache::find(const QString& key)
{
    QMutexLocker locker(&m_mutex);
    
    QPixmap* pixmap = m_cache.object(key);
    if (pixmap) {
        m_hitCount++;
        qDebug() << "Cache hit:" << key;
    } else {
        m_missCount++;
        qDebug() << "Cache miss:" << key;
    }
    
    return pixmap;
}

bool TileCache::contains(const QString& key) const
{
    QMutexLocker locker(&m_mutex);
    return m_cache.contains(key);
}

void TileCache::remove(const QString& key)
{
    QMutexLocker locker(&m_mutex);
    m_cache.remove(key);
    qDebug() << "Tile removed from cache:" << key;
}

void TileCache::clear()
{
    QMutexLocker locker(&m_mutex);
    m_cache.clear();
    resetStatistics();
    qDebug() << "Cache cleared";
}

void TileCache::setMaxCost(int maxCost)
{
    QMutexLocker locker(&m_mutex);
    m_cache.setMaxCost(maxCost);
    qDebug() << "Cache max cost set to:" << maxCost;
}

int TileCache::maxCost() const
{
    QMutexLocker locker(&m_mutex);
    return m_cache.maxCost();
}

int TileCache::totalCost() const
{
    QMutexLocker locker(&m_mutex);
    return m_cache.totalCost();
}

int TileCache::count() const
{
    QMutexLocker locker(&m_mutex);
    return m_cache.count();
}

quint64 TileCache::hitCount() const
{
    QMutexLocker locker(&m_mutex);
    return m_hitCount;
}

quint64 TileCache::missCount() const
{
    QMutexLocker locker(&m_mutex);
    return m_missCount;
}

double TileCache::hitRate() const
{
    QMutexLocker locker(&m_mutex);
    quint64 total = m_hitCount + m_missCount;
    return total > 0 ? static_cast<double>(m_hitCount) / total : 0.0;
}

void TileCache::resetStatistics()
{
    QMutexLocker locker(&m_mutex);
    m_hitCount = 0;
    m_missCount = 0;
    emit cacheStatisticsChanged();
}

void TileCache::updateStatistics()
{
    emit cacheStatisticsChanged();
}

void TileCache::onItemRemoved(const QString& key, QPixmap* pixmap)
{
    Q_UNUSED(key)
    delete pixmap;
}
