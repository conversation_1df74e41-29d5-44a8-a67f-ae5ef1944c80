#ifndef UDPRECEIVER_H
#define UDPRECEIVER_H

#include <QObject>
#include <QUdpSocket>
#include <QHostAddress>
#include <QTimer>
#include <QJsonObject>

class IMessageParser;

/**
 * @brief UDP组播接收器
 * 
 * 负责接收UDP组播数据并解析为目标信息
 */
class UdpReceiver : public QObject
{
    Q_OBJECT

public:
    explicit UdpReceiver(QObject *parent = nullptr);
    ~UdpReceiver();
    
    // 网络控制
    bool startListening();
    void stopListening();
    bool isListening() const;
    
    // 组播组管理
    bool joinMulticastGroup(const QHostAddress& address, quint16 port);
    bool leaveMulticastGroup(const QHostAddress& address, quint16 port);
    void clearMulticastGroups();
    
    // 消息解析器
    void setMessageParser(IMessageParser* parser);
    IMessageParser* messageParser() const;
    
    // 统计信息
    quint64 totalPacketsReceived() const;
    quint64 totalBytesReceived() const;
    quint64 validMessagesReceived() const;

signals:
    void messageReceived(const QJsonObject& message);
    void targetDataReceived(const QString& targetId, const QJsonObject& data);
    void networkStatusChanged(bool connected);
    void statisticsUpdated();

private slots:
    void processPendingDatagrams();
    void onSocketError(QAbstractSocket::SocketError error);
    void onSocketStateChanged(QAbstractSocket::SocketState state);
    void updateStatistics();

private:
    void setupSocket();
    void processMessage(const QByteArray& data, const QHostAddress& sender, quint16 port);
    void emitNetworkStatus();

private:
    QUdpSocket* m_socket;
    IMessageParser* m_parser;
    QTimer* m_statisticsTimer;
    
    // 组播组信息
    struct MulticastGroup {
        QHostAddress address;
        quint16 port;
        bool joined;
    };
    QList<MulticastGroup> m_groups;
    
    // 统计信息
    quint64 m_totalPackets;
    quint64 m_totalBytes;
    quint64 m_validMessages;
    bool m_isListening;
};

#endif // UDPRECEIVER_H
