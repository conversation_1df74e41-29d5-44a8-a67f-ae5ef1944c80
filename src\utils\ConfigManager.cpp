#include "ConfigManager.h"
#include "common/Constants.h"

#include <QFile>
#include <QDir>
#include <QJsonArray>
#include <QDebug>
#include <QStandardPaths>

ConfigManager& ConfigManager::instance()
{
    static ConfigManager instance;
    return instance;
}

ConfigManager::ConfigManager(QObject *parent)
    : QObject(parent)
    , m_configFile(Constants::CONFIG_FILE)
    , m_loaded(false)
{
    setDefaultValues();
}

bool ConfigManager::loadConfig(const QString& configFile)
{
    QString file = configFile.isEmpty() ? m_configFile : configFile;
    
    QFile jsonFile(file);
    if (!jsonFile.open(QIODevice::ReadOnly)) {
        qWarning() << "Cannot open config file:" << file;
        qInfo() << "Using default configuration";
        return false;
    }
    
    QByteArray data = jsonFile.readAll();
    QJsonParseError error;
    QJsonDocument doc = QJsonDocument::fromJson(data, &error);
    
    if (error.error != QJsonParseError::NoError) {
        qWarning() << "Config file parse error:" << error.errorString();
        return false;
    }
    
    m_config = doc.object();
    m_loaded = true;
    
    qInfo() << "Configuration loaded from:" << file;
    emit configChanged();
    return true;
}

bool ConfigManager::saveConfig(const QString& configFile)
{
    QString file = configFile.isEmpty() ? m_configFile : configFile;
    
    QFile jsonFile(file);
    if (!jsonFile.open(QIODevice::WriteOnly)) {
        qWarning() << "Cannot write config file:" << file;
        return false;
    }
    
    QJsonDocument doc(m_config);
    jsonFile.write(doc.toJson());
    
    qInfo() << "Configuration saved to:" << file;
    return true;
}

void ConfigManager::setDefaultValues()
{
    QJsonObject config;
    
    // 网络配置
    QJsonObject network;
    QJsonArray groups;
    
    QJsonObject group1;
    group1["address"] = Constants::DEFAULT_MULTICAST_GROUP;
    group1["port"] = Constants::DEFAULT_UDP_PORT;
    group1["enabled"] = true;
    group1["name"] = "Default Group";
    groups.append(group1);
    
    network["multicast_groups"] = groups;
    network["message_parser"] = "json";
    config["network"] = network;
    
    // 地图配置
    QJsonObject map;
    map["tile_source"] = Constants::DEFAULT_TILE_PATH;
    map["cache_size_mb"] = Constants::DEFAULT_CACHE_SIZE_MB;
    
    QJsonObject initialView;
    initialView["lat"] = 39.9042;  // 北京
    initialView["lon"] = 116.4074;
    map["initial_center"] = initialView;
    map["initial_zoom_level"] = Constants::DEFAULT_ZOOM_LEVEL;
    config["map"] = map;
    
    // 目标配置
    QJsonObject targets;
    targets["default_icon"] = ":/icons/target.png";
    targets["animation_duration"] = Constants::ANIMATION_DURATION;
    config["targets"] = targets;
    
    // 应用配置
    QJsonObject app;
    app["auto_start"] = false;
    app["log_level"] = "info";
    config["app"] = app;
    
    m_config = config;
}

QList<ConfigManager::MulticastGroup> ConfigManager::multicastGroups() const
{
    QList<MulticastGroup> groups;
    
    QJsonArray jsonGroups = m_config["network"].toObject()["multicast_groups"].toArray();
    for (const QJsonValue& value : jsonGroups) {
        groups.append(multicastGroupFromJson(value.toObject()));
    }
    
    return groups;
}

void ConfigManager::setMulticastGroups(const QList<MulticastGroup>& groups)
{
    QJsonArray jsonGroups;
    for (const MulticastGroup& group : groups) {
        jsonGroups.append(multicastGroupToJson(group));
    }
    
    QJsonObject network = m_config["network"].toObject();
    network["multicast_groups"] = jsonGroups;
    m_config["network"] = network;
    
    emit configChanged();
}

QString ConfigManager::messageParser() const
{
    return m_config["network"].toObject()["message_parser"].toString("json");
}

void ConfigManager::setMessageParser(const QString& parser)
{
    QJsonObject network = m_config["network"].toObject();
    network["message_parser"] = parser;
    m_config["network"] = network;
    emit configChanged();
}

QString ConfigManager::tileSource() const
{
    return m_config["map"].toObject()["tile_source"].toString(Constants::DEFAULT_TILE_PATH);
}

void ConfigManager::setTileSource(const QString& path)
{
    QJsonObject map = m_config["map"].toObject();
    map["tile_source"] = path;
    m_config["map"] = map;
    emit configChanged();
}

int ConfigManager::cacheSizeMB() const
{
    return m_config["map"].toObject()["cache_size_mb"].toInt(Constants::DEFAULT_CACHE_SIZE_MB);
}

void ConfigManager::setCacheSizeMB(int sizeMB)
{
    QJsonObject map = m_config["map"].toObject();
    map["cache_size_mb"] = sizeMB;
    m_config["map"] = map;
    emit configChanged();
}

QPointF ConfigManager::initialCenter() const
{
    QJsonObject center = m_config["map"].toObject()["initial_center"].toObject();
    return QPointF(center["lon"].toDouble(116.4074), center["lat"].toDouble(39.9042));
}

void ConfigManager::setInitialCenter(const QPointF& center)
{
    QJsonObject centerObj;
    centerObj["lat"] = center.y();
    centerObj["lon"] = center.x();
    
    QJsonObject map = m_config["map"].toObject();
    map["initial_center"] = centerObj;
    m_config["map"] = map;
    emit configChanged();
}

int ConfigManager::initialZoomLevel() const
{
    return m_config["map"].toObject()["initial_zoom_level"].toInt(Constants::DEFAULT_ZOOM_LEVEL);
}

void ConfigManager::setInitialZoomLevel(int level)
{
    QJsonObject map = m_config["map"].toObject();
    map["initial_zoom_level"] = level;
    m_config["map"] = map;
    emit configChanged();
}

QString ConfigManager::defaultIcon() const
{
    return m_config["targets"].toObject()["default_icon"].toString(":/icons/target.png");
}

void ConfigManager::setDefaultIcon(const QString& iconPath)
{
    QJsonObject targets = m_config["targets"].toObject();
    targets["default_icon"] = iconPath;
    m_config["targets"] = targets;
    emit configChanged();
}

int ConfigManager::animationDuration() const
{
    return m_config["targets"].toObject()["animation_duration"].toInt(Constants::ANIMATION_DURATION);
}

void ConfigManager::setAnimationDuration(int duration)
{
    QJsonObject targets = m_config["targets"].toObject();
    targets["animation_duration"] = duration;
    m_config["targets"] = targets;
    emit configChanged();
}

bool ConfigManager::autoStart() const
{
    return m_config["app"].toObject()["auto_start"].toBool(false);
}

void ConfigManager::setAutoStart(bool enabled)
{
    QJsonObject app = m_config["app"].toObject();
    app["auto_start"] = enabled;
    m_config["app"] = app;
    emit configChanged();
}

QString ConfigManager::logLevel() const
{
    return m_config["app"].toObject()["log_level"].toString("info");
}

void ConfigManager::setLogLevel(const QString& level)
{
    QJsonObject app = m_config["app"].toObject();
    app["log_level"] = level;
    m_config["app"] = app;
    emit configChanged();
}

QJsonObject ConfigManager::multicastGroupToJson(const MulticastGroup& group) const
{
    QJsonObject json;
    json["address"] = group.address.toString();
    json["port"] = group.port;
    json["enabled"] = group.enabled;
    json["name"] = group.name;
    return json;
}

ConfigManager::MulticastGroup ConfigManager::multicastGroupFromJson(const QJsonObject& json) const
{
    MulticastGroup group;
    group.address = QHostAddress(json["address"].toString());
    group.port = static_cast<quint16>(json["port"].toInt());
    group.enabled = json["enabled"].toBool(true);
    group.name = json["name"].toString();
    return group;
}
