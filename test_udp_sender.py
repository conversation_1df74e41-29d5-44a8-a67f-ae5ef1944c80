#!/usr/bin/env python3
"""
UDP测试发送器
用于测试QMapApp的UDP接收功能
"""

import socket
import json
import time
import random
import argparse
from datetime import datetime

class UdpTestSender:
    def __init__(self, multicast_group="************", port=15000):
        self.multicast_group = multicast_group
        self.port = port
        self.socket = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        self.socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
        
        # 设置组播TTL
        self.socket.setsockopt(socket.IPPROTO_IP, socket.IP_MULTICAST_TTL, 2)
        
        print(f"UDP发送器初始化完成，目标: {multicast_group}:{port}")
    
    def send_target_data(self, target_id, latitude, longitude, **kwargs):
        """发送目标数据"""
        message = {
            "type": "target_update",
            "id": target_id,
            "latitude": latitude,
            "longitude": longitude,
            "timestamp": datetime.now().isoformat()
        }
        
        # 添加可选字段
        if "name" in kwargs:
            message["name"] = kwargs["name"]
        if "altitude" in kwargs:
            message["altitude"] = kwargs["altitude"]
        if "heading" in kwargs:
            message["heading"] = kwargs["heading"]
        if "speed" in kwargs:
            message["speed"] = kwargs["speed"]
        if "type_name" in kwargs:
            message["type"] = kwargs["type_name"]
        if "status" in kwargs:
            message["status"] = kwargs["status"]
        
        json_data = json.dumps(message, ensure_ascii=False)
        self.socket.sendto(json_data.encode('utf-8'), (self.multicast_group, self.port))
        
        print(f"发送目标数据: {target_id} -> ({latitude:.6f}, {longitude:.6f})")
    
    def send_remove_target(self, target_id):
        """发送移除目标消息"""
        message = {
            "type": "target_remove",
            "id": target_id,
            "timestamp": datetime.now().isoformat()
        }
        
        json_data = json.dumps(message, ensure_ascii=False)
        self.socket.sendto(json_data.encode('utf-8'), (self.multicast_group, self.port))
        
        print(f"发送移除目标: {target_id}")
    
    def send_heartbeat(self):
        """发送心跳消息"""
        message = {
            "type": "heartbeat",
            "timestamp": datetime.now().isoformat()
        }
        
        json_data = json.dumps(message, ensure_ascii=False)
        self.socket.sendto(json_data.encode('utf-8'), (self.multicast_group, self.port))
        
        print("发送心跳")
    
    def simulate_moving_targets(self, count=5, duration=60):
        """模拟移动目标"""
        print(f"开始模拟 {count} 个移动目标，持续 {duration} 秒")
        
        # 初始化目标位置（北京周边）
        targets = {}
        base_lat, base_lon = 39.9042, 116.4074
        
        for i in range(count):
            target_id = f"target_{i+1:03d}"
            targets[target_id] = {
                "lat": base_lat + random.uniform(-0.1, 0.1),
                "lon": base_lon + random.uniform(-0.1, 0.1),
                "heading": random.uniform(0, 360),
                "speed": random.uniform(5, 30),
                "name": f"目标{i+1}",
                "type": random.choice(["vehicle", "aircraft", "ship"]),
                "status": "active"
            }
        
        start_time = time.time()
        last_heartbeat = 0
        
        while time.time() - start_time < duration:
            current_time = time.time()
            
            # 每10秒发送一次心跳
            if current_time - last_heartbeat > 10:
                self.send_heartbeat()
                last_heartbeat = current_time
            
            # 更新每个目标的位置
            for target_id, data in targets.items():
                # 简单的移动模拟
                speed_ms = data["speed"] / 3.6  # 转换为m/s
                distance_km = speed_ms * 1.0 / 1000  # 1秒移动的距离(km)
                
                # 计算新位置
                heading_rad = data["heading"] * 3.14159 / 180
                lat_delta = distance_km * 0.009 * random.uniform(0.8, 1.2)  # 大约1km = 0.009度
                lon_delta = distance_km * 0.009 * random.uniform(0.8, 1.2)
                
                data["lat"] += lat_delta * random.uniform(-1, 1)
                data["lon"] += lon_delta * random.uniform(-1, 1)
                data["heading"] += random.uniform(-10, 10)
                data["heading"] = data["heading"] % 360
                
                # 发送更新
                self.send_target_data(
                    target_id,
                    data["lat"],
                    data["lon"],
                    name=data["name"],
                    altitude=random.uniform(0, 1000),
                    heading=data["heading"],
                    speed=data["speed"],
                    type_name=data["type"],
                    status=data["status"]
                )
            
            time.sleep(1)  # 每秒更新一次
        
        print("模拟结束")
    
    def close(self):
        """关闭socket"""
        self.socket.close()

def main():
    parser = argparse.ArgumentParser(description="UDP测试发送器")
    parser.add_argument("--group", default="************", help="组播地址")
    parser.add_argument("--port", type=int, default=15000, help="端口号")
    parser.add_argument("--targets", type=int, default=5, help="目标数量")
    parser.add_argument("--duration", type=int, default=60, help="持续时间(秒)")
    
    args = parser.parse_args()
    
    sender = UdpTestSender(args.group, args.port)
    
    try:
        # 发送一些静态测试数据
        print("发送静态测试数据...")
        sender.send_target_data("test_001", 39.9042, 116.4074, 
                               name="测试目标1", status="active", type_name="vehicle")
        sender.send_target_data("test_002", 39.9142, 116.4174, 
                               name="测试目标2", status="active", type_name="aircraft")
        
        time.sleep(2)
        
        # 开始模拟移动目标
        sender.simulate_moving_targets(args.targets, args.duration)
        
    except KeyboardInterrupt:
        print("\n用户中断")
    finally:
        sender.close()

if __name__ == "__main__":
    main()
