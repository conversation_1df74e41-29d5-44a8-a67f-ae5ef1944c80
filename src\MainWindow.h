#ifndef MAINWINDOW_H
#define MAINWINDOW_H

#include <QMainWindow>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QMenuBar>
#include <QStatusBar>
#include <QToolBar>
#include <QAction>
#include <QLabel>
#include <QProgressBar>
#include <QSplitter>
#include <QDockWidget>
#include <QTreeView>

class MapWidget;
class TargetManager;
class UdpReceiver;
class TargetModel;

/**
 * @brief 主窗口类
 * 
 * 应用程序的主界面，负责整合各个功能模块
 */
class MainWindow : public QMainWindow
{
    Q_OBJECT

public:
    explicit MainWindow(QWidget *parent = nullptr);
    ~MainWindow();

protected:
    void closeEvent(QCloseEvent *event) override;

private slots:
    void onTargetUpdated(const QString& targetId);
    void onNetworkStatusChanged(bool connected);
    void onMapCenterChanged(double latitude, double longitude);
    void onZoomLevelChanged(int level);
    
    // 菜单动作槽函数
    void openConfig();
    void showAbout();
    void toggleFullScreen();
    void resetView();

private:
    void setupUI();
    void setupMenuBar();
    void setupToolBar();
    void setupStatusBar();
    void setupDockWidgets();
    void connectSignals();
    void loadSettings();
    void saveSettings();

private:
    // 核心组件
    MapWidget* m_mapWidget;
    TargetManager* m_targetManager;
    UdpReceiver* m_udpReceiver;
    TargetModel* m_targetModel;
    
    // UI组件
    QWidget* m_centralWidget;
    QVBoxLayout* m_mainLayout;
    QSplitter* m_mainSplitter;
    
    // 停靠窗口
    QDockWidget* m_targetDock;
    QTreeView* m_targetView;
    
    // 菜单和工具栏
    QMenuBar* m_menuBar;
    QToolBar* m_toolBar;
    QStatusBar* m_statusBar;
    
    // 菜单动作
    QAction* m_openConfigAction;
    QAction* m_exitAction;
    QAction* m_aboutAction;
    QAction* m_fullScreenAction;
    QAction* m_resetViewAction;
    
    // 状态栏组件
    QLabel* m_coordinateLabel;
    QLabel* m_zoomLabel;
    QLabel* m_targetCountLabel;
    QLabel* m_networkStatusLabel;
    QProgressBar* m_loadingProgress;
};

#endif // MAINWINDOW_H
