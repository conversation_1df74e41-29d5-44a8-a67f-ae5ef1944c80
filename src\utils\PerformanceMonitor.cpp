#include "PerformanceMonitor.h"
#include <QApplication>
#include <QDebug>
#include <QProcess>
#include <QDateTime>

#ifdef Q_OS_WIN
#include <windows.h>
#include <psapi.h>
#elif defined(Q_OS_LINUX)
#include <unistd.h>
#include <fstream>
#elif defined(Q_OS_MAC)
#include <mach/mach.h>
#endif

// 常量定义
const int PerformanceMonitor::MAX_HISTORY_SIZE = 300; // 5分钟历史数据(1秒间隔)
const double PerformanceMonitor::MIN_FRAME_RATE = 30.0;
const qint64 PerformanceMonitor::MAX_MEMORY_USAGE_MB = 1024; // 1GB
const double PerformanceMonitor::MIN_CACHE_HIT_RATE = 0.8; // 80%

PerformanceMonitor& PerformanceMonitor::instance()
{
    static PerformanceMonitor instance;
    return instance;
}

PerformanceMonitor::PerformanceMonitor(QObject *parent)
    : QObject(parent)
    , m_timer(new QTimer(this))
    , m_lastFrameTime(0)
    , m_monitoring(false)
{
    // 初始化指标
    m_currentMetrics = {};
    
    // 设置定时器
    connect(m_timer, &QTimer::timeout, this, &PerformanceMonitor::collectMetrics);
    
    m_elapsedTimer.start();
    
    qDebug() << "PerformanceMonitor initialized";
}

void PerformanceMonitor::startMonitoring(int intervalMs)
{
    if (m_monitoring) {
        return;
    }
    
    m_timer->setInterval(intervalMs);
    m_timer->start();
    m_monitoring = true;
    
    qInfo() << "Performance monitoring started with interval:" << intervalMs << "ms";
}

void PerformanceMonitor::stopMonitoring()
{
    if (!m_monitoring) {
        return;
    }
    
    m_timer->stop();
    m_monitoring = false;
    
    qInfo() << "Performance monitoring stopped";
}

bool PerformanceMonitor::isMonitoring() const
{
    return m_monitoring;
}

void PerformanceMonitor::updateFrameRate(double fps)
{
    QMutexLocker locker(&m_mutex);
    
    qint64 currentTime = m_elapsedTimer.elapsed();
    m_frameTimes.append(currentTime);
    
    // 保持最近1秒的帧时间
    while (!m_frameTimes.isEmpty() && currentTime - m_frameTimes.first() > 1000) {
        m_frameTimes.removeFirst();
    }
    
    // 计算实际帧率
    if (m_frameTimes.size() > 1) {
        m_currentMetrics.frameRate = (m_frameTimes.size() - 1) * 1000.0 / 
                                    (m_frameTimes.last() - m_frameTimes.first());
    } else {
        m_currentMetrics.frameRate = fps;
    }
}

void PerformanceMonitor::updateMemoryUsage(qint64 memoryMB)
{
    QMutexLocker locker(&m_mutex);
    m_currentMetrics.memoryUsageMB = memoryMB;
    m_currentMetrics.peakMemoryUsageMB = qMax(m_currentMetrics.peakMemoryUsageMB, memoryMB);
}

void PerformanceMonitor::updateNetworkStats(quint64 packets, quint64 bytes, quint64 validMessages)
{
    QMutexLocker locker(&m_mutex);
    m_currentMetrics.packetsReceived = packets;
    m_currentMetrics.bytesReceived = bytes;
    m_currentMetrics.validMessages = validMessages;
}

void PerformanceMonitor::updateTileStats(int loaded, int cached, double hitRate)
{
    QMutexLocker locker(&m_mutex);
    m_currentMetrics.tilesLoaded = loaded;
    m_currentMetrics.tilesInCache = cached;
    m_currentMetrics.cacheHitRate = hitRate;
}

void PerformanceMonitor::updateTargetStats(int active, int total)
{
    QMutexLocker locker(&m_mutex);
    m_currentMetrics.activeTargets = active;
    m_currentMetrics.totalTargets = total;
}

PerformanceMonitor::PerformanceMetrics PerformanceMonitor::getCurrentMetrics() const
{
    QMutexLocker locker(&m_mutex);
    return m_currentMetrics;
}

QList<PerformanceMonitor::PerformanceMetrics> PerformanceMonitor::getHistoryMetrics(int count) const
{
    QMutexLocker locker(&m_mutex);
    
    int startIndex = qMax(0, m_history.size() - count);
    return m_history.mid(startIndex);
}

double PerformanceMonitor::getAverageFrameRate(int seconds) const
{
    QMutexLocker locker(&m_mutex);
    
    if (m_history.isEmpty()) {
        return m_currentMetrics.frameRate;
    }
    
    qint64 cutoffTime = QDateTime::currentMSecsSinceEpoch() - seconds * 1000;
    double totalFps = 0.0;
    int count = 0;
    
    for (const PerformanceMetrics& metrics : m_history) {
        if (metrics.timestamp >= cutoffTime) {
            totalFps += metrics.frameRate;
            count++;
        }
    }
    
    return count > 0 ? totalFps / count : m_currentMetrics.frameRate;
}

qint64 PerformanceMonitor::getAverageMemoryUsage(int seconds) const
{
    QMutexLocker locker(&m_mutex);
    
    if (m_history.isEmpty()) {
        return m_currentMetrics.memoryUsageMB;
    }
    
    qint64 cutoffTime = QDateTime::currentMSecsSinceEpoch() - seconds * 1000;
    qint64 totalMemory = 0;
    int count = 0;
    
    for (const PerformanceMetrics& metrics : m_history) {
        if (metrics.timestamp >= cutoffTime) {
            totalMemory += metrics.memoryUsageMB;
            count++;
        }
    }
    
    return count > 0 ? totalMemory / count : m_currentMetrics.memoryUsageMB;
}

bool PerformanceMonitor::isPerformanceGood() const
{
    QMutexLocker locker(&m_mutex);
    
    return m_currentMetrics.frameRate >= MIN_FRAME_RATE &&
           m_currentMetrics.memoryUsageMB <= MAX_MEMORY_USAGE_MB &&
           m_currentMetrics.cacheHitRate >= MIN_CACHE_HIT_RATE;
}

void PerformanceMonitor::collectMetrics()
{
    QMutexLocker locker(&m_mutex);
    
    // 更新内存使用
    m_currentMetrics.memoryUsageMB = getCurrentMemoryUsage();
    m_currentMetrics.peakMemoryUsageMB = qMax(m_currentMetrics.peakMemoryUsageMB, 
                                             m_currentMetrics.memoryUsageMB);
    
    // 设置时间戳
    m_currentMetrics.timestamp = QDateTime::currentMSecsSinceEpoch();
    
    // 添加到历史记录
    m_history.append(m_currentMetrics);
    
    // 限制历史记录大小
    while (m_history.size() > MAX_HISTORY_SIZE) {
        m_history.removeFirst();
    }
    
    // 检查性能阈值
    checkPerformanceThresholds(m_currentMetrics);
    
    // 发射信号
    emit metricsUpdated(m_currentMetrics);
}

qint64 PerformanceMonitor::getCurrentMemoryUsage() const
{
#ifdef Q_OS_WIN
    PROCESS_MEMORY_COUNTERS pmc;
    if (GetProcessMemoryInfo(GetCurrentProcess(), &pmc, sizeof(pmc))) {
        return pmc.WorkingSetSize / (1024 * 1024); // 转换为MB
    }
#elif defined(Q_OS_LINUX)
    std::ifstream file("/proc/self/status");
    std::string line;
    while (std::getline(file, line)) {
        if (line.substr(0, 6) == "VmRSS:") {
            std::string value = line.substr(6);
            return std::stoll(value) / 1024; // 转换为MB
        }
    }
#elif defined(Q_OS_MAC)
    struct task_basic_info info;
    mach_msg_type_number_t size = sizeof(info);
    kern_return_t kerr = task_info(mach_task_self(), TASK_BASIC_INFO, 
                                  (task_info_t)&info, &size);
    if (kerr == KERN_SUCCESS) {
        return info.resident_size / (1024 * 1024); // 转换为MB
    }
#endif
    
    // 如果无法获取系统内存信息，返回估算值
    return 100; // 默认100MB
}

void PerformanceMonitor::checkPerformanceThresholds(const PerformanceMetrics& metrics)
{
    // 检查帧率
    if (metrics.frameRate < MIN_FRAME_RATE) {
        emit performanceWarning(QString("帧率过低: %1 FPS").arg(metrics.frameRate, 0, 'f', 1));
    }
    
    // 检查内存使用
    if (metrics.memoryUsageMB > MAX_MEMORY_USAGE_MB) {
        emit performanceWarning(QString("内存使用过高: %1 MB").arg(metrics.memoryUsageMB));
    }
    
    // 检查缓存命中率
    if (metrics.cacheHitRate < MIN_CACHE_HIT_RATE && metrics.tilesInCache > 0) {
        emit performanceWarning(QString("缓存命中率过低: %1%").arg(metrics.cacheHitRate * 100, 0, 'f', 1));
    }
}
