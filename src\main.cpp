#include <QApplication>
#include <QDir>
#include <QStandardPaths>
#include <QLoggingCategory>
#include <QDebug>

#include "MainWindow.h"
#include "common/Constants.h"
#include "utils/ConfigManager.h"

Q_LOGGING_CATEGORY(main, "main")

/**
 * @brief 初始化应用程序目录
 */
void initializeDirectories() {
    QStringList dirs = {
        Constants::CACHE_DIR,
        Constants::LOG_DIR,
        Constants::DEFAULT_TILE_PATH
    };
    
    for (const QString& dirPath : dirs) {
        QDir dir(dirPath);
        if (!dir.exists()) {
            if (dir.mkpath(".")) {
                qCDebug(main) << "Created directory:" << dirPath;
            } else {
                qCWarning(main) << "Failed to create directory:" << dirPath;
            }
        }
    }
}

/**
 * @brief 设置应用程序属性
 */
void setupApplication(QApplication& app) {
    app.setApplicationName(Constants::APP_NAME);
    app.setOrganizationName(Constants::APP_ORGANIZATION);
    app.setOrganizationDomain(Constants::APP_DOMAIN);
    app.setApplicationVersion(APP_VERSION);
    
    // 设置应用程序图标
    app.setWindowIcon(QIcon(":/icons/app.png"));
}

int main(int argc, char *argv[])
{
    QApplication app(argc, argv);
    
    // 设置应用程序属性
    setupApplication(app);
    
    // 初始化目录结构
    initializeDirectories();
    
    // 初始化配置管理器
    ConfigManager::instance().loadConfig();
    
    qCInfo(main) << "Starting" << Constants::APP_NAME << "version" << APP_VERSION;
    
    // 创建主窗口
    MainWindow window;
    window.show();
    
    int result = app.exec();
    
    qCInfo(main) << "Application exiting with code:" << result;
    return result;
}
