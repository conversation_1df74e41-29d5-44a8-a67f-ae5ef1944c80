#include "TileLoader.h"
#include "TileCache.h"
#include "common/Constants.h"
#include "utils/CoordinateConverter.h"
#include <QDir>
#include <QPixmap>
#include <QDebug>
#include <QCoreApplication>

// TileLoadTask 实现
TileLoadTask::TileLoadTask(int x, int y, int z, const QString& tilePath)
    : m_x(x), m_y(y), m_z(z), m_tilePath(tilePath)
{
    setAutoDelete(true);
}

void TileLoadTask::run()
{
    QString filePath = QString("%1/%2/%3/%4.png")
                      .arg(m_tilePath)
                      .arg(m_z)
                      .arg(m_x)
                      .arg(m_y);
    
    QPixmap pixmap;
    if (pixmap.load(filePath)) {
        emit tileLoaded(m_x, m_y, m_z, pixmap);
    } else {
        // 尝试加载jpg格式
        filePath = QString("%1/%2/%3/%4.jpg")
                  .arg(m_tilePath)
                  .arg(m_z)
                  .arg(m_x)
                  .arg(m_y);
        
        if (pixmap.load(filePath)) {
            emit tileLoaded(m_x, m_y, m_z, pixmap);
        } else {
            emit tileLoadFailed(m_x, m_y, m_z);
        }
    }
}

// TileLoader 实现
TileLoader::TileLoader(QObject *parent)
    : QObject(parent)
    , m_threadPool(new QThreadPool(this))
    , m_cache(new TileCache(this))
    , m_tileSource(Constants::DEFAULT_TILE_PATH)
    , m_totalRequested(0)
    , m_totalLoaded(0)
{
    // 设置线程池参数
    m_threadPool->setMaxThreadCount(QThread::idealThreadCount());
    
    qDebug() << "TileLoader initialized with" << m_threadPool->maxThreadCount() << "threads";
}

TileLoader::~TileLoader()
{
    clearQueue();
}

void TileLoader::setTileSource(const QString& tilePath)
{
    QMutexLocker locker(&m_mutex);
    m_tileSource = tilePath;
    
    // 清除缓存，因为瓦片源已更改
    m_cache->clear();
    
    qDebug() << "Tile source set to:" << tilePath;
}

void TileLoader::loadTiles(const QRect& tileRect, int zoomLevel)
{
    if (tileRect.isEmpty() || !CoordinateConverter::isValidZoomLevel(zoomLevel)) {
        return;
    }
    
    QMutexLocker locker(&m_mutex);
    
    // 清除之前的请求统计
    m_requestedTiles.clear();
    m_totalRequested = 0;
    m_totalLoaded = 0;
    
    // 遍历瓦片矩形区域
    for (int x = tileRect.left(); x <= tileRect.right(); ++x) {
        for (int y = tileRect.top(); y <= tileRect.bottom(); ++y) {
            QString key = tileKey(x, y, zoomLevel);
            
            // 检查是否已在缓存中
            if (m_cache->contains(key)) {
                continue;
            }
            
            // 检查是否已在加载队列中
            if (m_loadingTiles.contains(key)) {
                continue;
            }
            
            // 添加到加载队列
            m_loadingTiles.insert(key);
            m_requestedTiles.insert(key);
            m_totalRequested++;
            
            // 创建加载任务
            TileLoadTask* task = new TileLoadTask(x, y, zoomLevel, m_tileSource);
            connect(task, &TileLoadTask::tileLoaded, 
                   this, &TileLoader::onTileLoaded, Qt::QueuedConnection);
            connect(task, &TileLoadTask::tileLoadFailed, 
                   this, &TileLoader::onTileLoadFailed, Qt::QueuedConnection);
            
            // 提交到线程池
            m_threadPool->start(task);
        }
    }
    
    updateProgress();
    
    qDebug() << "Loading" << m_totalRequested << "tiles for zoom level" << zoomLevel;
}

void TileLoader::clearQueue()
{
    QMutexLocker locker(&m_mutex);
    
    m_threadPool->clear();
    m_loadingTiles.clear();
    m_requestedTiles.clear();
    m_totalRequested = 0;
    m_totalLoaded = 0;
    
    qDebug() << "Tile loading queue cleared";
}

void TileLoader::setCacheSize(int sizeMB)
{
    m_cache->setMaxCost(sizeMB * 1024 * 1024);
}

void TileLoader::clearCache()
{
    m_cache->clear();
}

QPixmap TileLoader::getTile(int x, int y, int z)
{
    QString key = tileKey(x, y, z);
    QPixmap* cachedPixmap = m_cache->find(key);
    
    if (cachedPixmap) {
        return *cachedPixmap;
    }
    
    return QPixmap();
}

bool TileLoader::hasTile(int x, int y, int z)
{
    QString key = tileKey(x, y, z);
    return m_cache->contains(key);
}

void TileLoader::onTileLoaded(int x, int y, int z, const QPixmap& pixmap)
{
    QString key = tileKey(x, y, z);
    
    {
        QMutexLocker locker(&m_mutex);
        
        // 从加载队列中移除
        m_loadingTiles.remove(key);
        
        // 更新统计
        if (m_requestedTiles.contains(key)) {
            m_totalLoaded++;
        }
    }
    
    // 添加到缓存
    m_cache->insert(key, pixmap);
    
    // 发射信号
    emit tileLoaded(x, y, z, pixmap);
    
    updateProgress();
}

void TileLoader::onTileLoadFailed(int x, int y, int z)
{
    QString key = tileKey(x, y, z);
    
    {
        QMutexLocker locker(&m_mutex);
        
        // 从加载队列中移除
        m_loadingTiles.remove(key);
        
        // 更新统计
        if (m_requestedTiles.contains(key)) {
            m_totalLoaded++;
        }
    }
    
    qWarning() << "Failed to load tile:" << x << y << z;
    
    updateProgress();
}

QString TileLoader::getTilePath(int x, int y, int z) const
{
    return QString("%1/%2/%3/%4.png").arg(m_tileSource).arg(z).arg(x).arg(y);
}

QString TileLoader::tileKey(int x, int y, int z) const
{
    return QString("%1_%2_%3").arg(z).arg(x).arg(y);
}

void TileLoader::updateProgress()
{
    QMutexLocker locker(&m_mutex);
    emit loadingProgress(m_totalLoaded, m_totalRequested);
}
