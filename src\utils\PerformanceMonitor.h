#ifndef PERFORMANCEMONITOR_H
#define PERFORMANCEMONITOR_H

#include <QObject>
#include <QTimer>
#include <QElapsedTimer>
#include <QMap>
#include <QMutex>

/**
 * @brief 性能监控器
 * 
 * 监控应用程序的性能指标，包括内存使用、帧率、网络统计等
 */
class PerformanceMonitor : public QObject
{
    Q_OBJECT

public:
    struct PerformanceMetrics {
        // 内存指标
        qint64 memoryUsageMB;
        qint64 peakMemoryUsageMB;
        
        // 渲染指标
        double frameRate;
        int droppedFrames;
        
        // 网络指标
        quint64 packetsReceived;
        quint64 bytesReceived;
        quint64 validMessages;
        
        // 瓦片指标
        int tilesLoaded;
        int tilesInCache;
        double cacheHitRate;
        
        // 目标指标
        int activeTargets;
        int totalTargets;
        
        // 时间戳
        qint64 timestamp;
    };

    static PerformanceMonitor& instance();
    
    // 监控控制
    void startMonitoring(int intervalMs = 1000);
    void stopMonitoring();
    bool isMonitoring() const;
    
    // 指标更新
    void updateFrameRate(double fps);
    void updateMemoryUsage(qint64 memoryMB);
    void updateNetworkStats(quint64 packets, quint64 bytes, quint64 validMessages);
    void updateTileStats(int loaded, int cached, double hitRate);
    void updateTargetStats(int active, int total);
    
    // 指标获取
    PerformanceMetrics getCurrentMetrics() const;
    QList<PerformanceMetrics> getHistoryMetrics(int count = 60) const;
    
    // 性能分析
    double getAverageFrameRate(int seconds = 10) const;
    qint64 getAverageMemoryUsage(int seconds = 10) const;
    bool isPerformanceGood() const;

signals:
    void metricsUpdated(const PerformanceMetrics& metrics);
    void performanceWarning(const QString& message);

private slots:
    void collectMetrics();

private:
    explicit PerformanceMonitor(QObject *parent = nullptr);
    ~PerformanceMonitor() = default;
    
    // 禁用拷贝构造和赋值
    PerformanceMonitor(const PerformanceMonitor&) = delete;
    PerformanceMonitor& operator=(const PerformanceMonitor&) = delete;
    
    qint64 getCurrentMemoryUsage() const;
    void checkPerformanceThresholds(const PerformanceMetrics& metrics);

private:
    QTimer* m_timer;
    QElapsedTimer m_elapsedTimer;
    mutable QMutex m_mutex;
    
    // 当前指标
    PerformanceMetrics m_currentMetrics;
    
    // 历史数据
    QList<PerformanceMetrics> m_history;
    static const int MAX_HISTORY_SIZE;
    
    // 性能阈值
    static const double MIN_FRAME_RATE;
    static const qint64 MAX_MEMORY_USAGE_MB;
    static const double MIN_CACHE_HIT_RATE;
    
    // 帧率计算
    QList<qint64> m_frameTimes;
    qint64 m_lastFrameTime;
    
    bool m_monitoring;
};

#endif // PERFORMANCEMONITOR_H
