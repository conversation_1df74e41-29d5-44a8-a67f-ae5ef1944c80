#!/bin/bash
# QMapApp 构建脚本 (Linux/macOS)
# 需要安装Qt 5.14或更高版本

echo "========================================"
echo "QMapApp 构建脚本"
echo "========================================"

# 检查qmake是否可用
if ! command -v qmake &> /dev/null; then
    echo "错误: 找不到qmake命令"
    echo "请确保Qt已正确安装并添加到PATH环境变量中"
    exit 1
fi

# 显示Qt版本
echo "检测到的Qt版本:"
qmake -version

# 创建构建目录
mkdir -p build
cd build

echo ""
echo "正在生成Makefile..."
qmake ../QMapApp.pro
if [ $? -ne 0 ]; then
    echo "错误: qmake失败"
    exit 1
fi

echo ""
echo "正在编译项目..."
make -j$(nproc 2>/dev/null || sysctl -n hw.ncpu 2>/dev/null || echo 4)
if [ $? -ne 0 ]; then
    echo "错误: 编译失败"
    exit 1
fi

echo ""
echo "========================================"
echo "编译成功！"
echo "========================================"
echo "可执行文件位置: build/bin/QMapApp"
echo ""

# 检查是否需要生成测试瓦片
if [ ! -d "../map_tiles" ]; then
    echo "检测到没有地图瓦片，是否生成测试瓦片？"
    read -p "输入 y 生成测试瓦片，其他键跳过: " generate_tiles
    if [ "$generate_tiles" = "y" ] || [ "$generate_tiles" = "Y" ]; then
        echo "正在生成测试瓦片..."
        cd ..
        python3 generate_test_tiles.py
        cd build
    fi
fi

echo ""
echo "要运行应用程序，请执行:"
echo "  cd build/bin"
echo "  ./QMapApp"
echo ""
echo "要测试UDP功能，请在另一个终端运行:"
echo "  python3 test_udp_sender.py"
echo ""
