#include "TargetItem.h"
#include "common/Constants.h"
#include "utils/ConfigManager.h"
#include <QGraphicsScene>
#include <QGraphicsSceneMouseEvent>
#include <QPropertyAnimation>
#include <QDebug>

// 常量定义
const int TargetItem::MAX_TRAIL_POINTS = 50;
const QColor TargetItem::LABEL_BACKGROUND_COLOR = QColor(0, 0, 0, 180);
const QColor TargetItem::LABEL_TEXT_COLOR = Qt::white;
const QColor TargetItem::TRAIL_COLOR = QColor(255, 0, 0, 128);
const QColor TargetItem::SELECTION_COLOR = Qt::yellow;
const int TargetItem::LABEL_MARGIN = 5;
const int TargetItem::SELECTION_MARGIN = 3;

TargetItem::TargetItem(const Target& target, QGraphicsItem *parent)
    : QGraphicsPixmapItem(parent)
    , m_target(target)
    , m_iconSize(Constants::TARGET_ICON_SIZE, Constants::TARGET_ICON_SIZE)
    , m_showLabel(true)
    , m_showTrail(false)
    , m_highlighted(false)
    , m_hovered(false)
    , m_positionAnimation(nullptr)
    , m_rotationAnimation(nullptr)
    , m_opacityAnimation(nullptr)
{
    setAcceptHoverEvents(true);
    setFlag(QGraphicsItem::ItemIsSelectable, true);
    
    setupAnimations();
    updateAppearance();
    
    qDebug() << "TargetItem created for target:" << target.id;
}

TargetItem::~TargetItem()
{
    qDebug() << "TargetItem destroyed for target:" << m_target.id;
}

void TargetItem::setupAnimations()
{
    // 位置动画
    m_positionAnimation = new QPropertyAnimation(this, "position");
    m_positionAnimation->setDuration(ConfigManager::instance().animationDuration());
    m_positionAnimation->setEasingCurve(QEasingCurve::OutCubic);
    connect(m_positionAnimation, &QPropertyAnimation::finished,
            this, &TargetItem::onPositionAnimationFinished);
    
    // 旋转动画
    m_rotationAnimation = new QPropertyAnimation(this, "rotation");
    m_rotationAnimation->setDuration(500);
    m_rotationAnimation->setEasingCurve(QEasingCurve::OutCubic);
    connect(m_rotationAnimation, &QPropertyAnimation::finished,
            this, &TargetItem::onRotationAnimationFinished);
    
    // 透明度动画
    m_opacityAnimation = new QPropertyAnimation(this, "opacity");
    m_opacityAnimation->setDuration(500);
    m_opacityAnimation->setEasingCurve(QEasingCurve::InOutQuad);
}

void TargetItem::updateTarget(const Target& target)
{
    m_target = target;
    updateAppearance();
    
    // 添加轨迹点
    if (m_showTrail) {
        addTrailPoint(pos());
    }
    
    update();
}

const Target& TargetItem::target() const
{
    return m_target;
}

QString TargetItem::targetId() const
{
    return m_target.id;
}

void TargetItem::setIcon(const QPixmap& icon)
{
    m_icon = icon.scaled(m_iconSize, Qt::KeepAspectRatio, Qt::SmoothTransformation);
    setPixmap(m_icon);
}

void TargetItem::setIconSize(const QSize& size)
{
    m_iconSize = size;
    if (!m_icon.isNull()) {
        setIcon(m_icon);
    }
}

void TargetItem::setShowLabel(bool show)
{
    m_showLabel = show;
    update();
}

void TargetItem::setShowTrail(bool show)
{
    m_showTrail = show;
    if (!show) {
        m_trailPoints.clear();
    }
    update();
}

void TargetItem::setHighlighted(bool highlighted)
{
    m_highlighted = highlighted;
    update();
}

void TargetItem::animateToPosition(const QPointF& newPos, int duration)
{
    if (m_positionAnimation->state() == QAbstractAnimation::Running) {
        m_positionAnimation->stop();
    }
    
    m_positionAnimation->setDuration(duration);
    m_positionAnimation->setStartValue(pos());
    m_positionAnimation->setEndValue(newPos);
    m_positionAnimation->start();
}

void TargetItem::animateRotation(qreal newRotation, int duration)
{
    if (m_rotationAnimation->state() == QAbstractAnimation::Running) {
        m_rotationAnimation->stop();
    }
    
    m_rotationAnimation->setDuration(duration);
    m_rotationAnimation->setStartValue(rotation());
    m_rotationAnimation->setEndValue(newRotation);
    m_rotationAnimation->start();
}

void TargetItem::animateFadeIn(int duration)
{
    if (m_opacityAnimation->state() == QAbstractAnimation::Running) {
        m_opacityAnimation->stop();
    }
    
    m_opacityAnimation->setDuration(duration);
    m_opacityAnimation->setStartValue(0.0);
    m_opacityAnimation->setEndValue(1.0);
    m_opacityAnimation->start();
}

void TargetItem::animateFadeOut(int duration)
{
    if (m_opacityAnimation->state() == QAbstractAnimation::Running) {
        m_opacityAnimation->stop();
    }
    
    m_opacityAnimation->setDuration(duration);
    m_opacityAnimation->setStartValue(opacity());
    m_opacityAnimation->setEndValue(0.0);
    m_opacityAnimation->start();
}

QRectF TargetItem::boundingRect() const
{
    QRectF rect = QGraphicsPixmapItem::boundingRect();
    QFont a;
    if (m_showLabel && !m_target.name.isEmpty()) {
        QFontMetrics fm(a);
        QRect textRect = fm.boundingRect(m_target.name);
        textRect.adjust(-LABEL_MARGIN, -LABEL_MARGIN, LABEL_MARGIN, LABEL_MARGIN);
        textRect.moveTop(rect.bottom() + 5);
        rect = rect.united(textRect);
    }
    
    if (m_highlighted) {
        rect.adjust(-SELECTION_MARGIN, -SELECTION_MARGIN, SELECTION_MARGIN, SELECTION_MARGIN);
    }
    
    return rect;
}

void TargetItem::paint(QPainter *painter, const QStyleOptionGraphicsItem *option, QWidget *widget)
{
    Q_UNUSED(option)
    Q_UNUSED(widget)
    
    painter->setRenderHint(QPainter::Antialiasing);
    
    // 绘制轨迹
    if (m_showTrail && m_trailPoints.size() > 1) {
        drawTrail(painter);
    }
    
    // 绘制选择指示器
    if (m_highlighted || isSelected()) {
        drawSelectionIndicator(painter);
    }
    
    // 绘制图标
    QGraphicsPixmapItem::paint(painter, option, widget);
    
    // 绘制标签
    if (m_showLabel && !m_target.name.isEmpty()) {
        drawLabel(painter);
    }
}

QPainterPath TargetItem::shape() const
{
    QPainterPath path;
    path.addEllipse(boundingRect());
    return path;
}

void TargetItem::mousePressEvent(QGraphicsSceneMouseEvent *event)
{
    if (event->button() == Qt::LeftButton) {
        emit clicked(m_target.id);
    }
    QGraphicsPixmapItem::mousePressEvent(event);
}

void TargetItem::mouseDoubleClickEvent(QGraphicsSceneMouseEvent *event)
{
    if (event->button() == Qt::LeftButton) {
        emit doubleClicked(m_target.id);
    }
    QGraphicsPixmapItem::mouseDoubleClickEvent(event);
}

void TargetItem::hoverEnterEvent(QGraphicsSceneHoverEvent *event)
{
    Q_UNUSED(event)
    m_hovered = true;
    update();
}

void TargetItem::hoverLeaveEvent(QGraphicsSceneHoverEvent *event)
{
    Q_UNUSED(event)
    m_hovered = false;
    update();
}

void TargetItem::onPositionAnimationFinished()
{
    emit positionChanged(m_target.id, pos());
}

void TargetItem::onRotationAnimationFinished()
{
    // 旋转动画完成后的处理
}

void TargetItem::updateAppearance()
{
    // 加载图标
    QString iconPath = m_target.iconPath.isEmpty() ? 
                      ConfigManager::instance().defaultIcon() : 
                      m_target.iconPath;
    
    QPixmap icon(iconPath);
    if (icon.isNull()) {
        // 创建默认图标
        icon = QPixmap(m_iconSize);
        icon.fill(Qt::red);
    }
    
    setIcon(icon);
    
    // 设置旋转角度
    setRotation(m_target.heading);
}

void TargetItem::drawLabel(QPainter *painter)
{
    QFont font = painter->font();
    QFontMetrics fm(font);
    QRect textRect = fm.boundingRect(m_target.name);
    
    // 计算标签位置
    QRectF iconRect = QGraphicsPixmapItem::boundingRect();
    QRectF labelRect = textRect;
    labelRect.adjust(-LABEL_MARGIN, -LABEL_MARGIN, LABEL_MARGIN, LABEL_MARGIN);
    labelRect.moveCenter(QPointF(iconRect.center().x(), iconRect.bottom() + labelRect.height()/2 + 5));
    
    // 绘制背景
    painter->setBrush(LABEL_BACKGROUND_COLOR);
    painter->setPen(Qt::NoPen);
    painter->drawRoundedRect(labelRect, 3, 3);
    
    // 绘制文本
    painter->setPen(LABEL_TEXT_COLOR);
    painter->drawText(labelRect, Qt::AlignCenter, m_target.name);
}

void TargetItem::drawTrail(QPainter *painter)
{
    if (m_trailPoints.size() < 2) return;
    
    QPen pen(TRAIL_COLOR);
    pen.setWidth(2);
    painter->setPen(pen);
    
    for (int i = 1; i < m_trailPoints.size(); ++i) {
        painter->drawLine(m_trailPoints[i-1], m_trailPoints[i]);
    }
}

void TargetItem::drawSelectionIndicator(QPainter *painter)
{
    QRectF rect = QGraphicsPixmapItem::boundingRect();
    rect.adjust(-SELECTION_MARGIN, -SELECTION_MARGIN, SELECTION_MARGIN, SELECTION_MARGIN);
    
    QPen pen(SELECTION_COLOR);
    pen.setWidth(2);
    painter->setPen(pen);
    painter->setBrush(Qt::NoBrush);
    painter->drawEllipse(rect);
}

void TargetItem::addTrailPoint(const QPointF& point)
{
    m_trailPoints.append(point);
    
    // 限制轨迹点数量
    while (m_trailPoints.size() > MAX_TRAIL_POINTS) {
        m_trailPoints.removeFirst();
    }
}
