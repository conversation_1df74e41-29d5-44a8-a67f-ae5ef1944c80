# QMapApp - Qt离线地图应用程序
# 基于Qt 5.14的高性能桌面地图应用

QT += core gui network concurrent
greaterThan(QT_MAJOR_VERSION, 4): QT += widgets

CONFIG += c++17
TARGET = QMapApp
TEMPLATE = app

# 版本信息
VERSION = 1.0.0
DEFINES += APP_VERSION=\\\"$$VERSION\\\"

# 源代码结构
SOURCES += \
    src/main.cpp \
    src/MainWindow.cpp \
    src/map/MapWidget.cpp \
    src/map/TileLoader.cpp \
    src/map/TileCache.cpp \
    src/network/UdpReceiver.cpp \
    src/network/MessageParser.cpp \
    src/models/TargetModel.cpp \
    src/managers/TargetManager.cpp \
    src/utils/CoordinateConverter.cpp \
    src/utils/ConfigManager.cpp \
    src/utils/PerformanceMonitor.cpp \
    src/graphics/TargetItem.cpp

HEADERS += \
    src/MainWindow.h \
    src/map/MapWidget.h \
    src/map/TileLoader.h \
    src/map/TileCache.h \
    src/network/UdpReceiver.h \
    src/network/IMessageParser.h \
    src/network/MessageParser.h \
    src/models/TargetModel.h \
    src/managers/TargetManager.h \
    src/utils/CoordinateConverter.h \
    src/utils/ConfigManager.h \
    src/utils/PerformanceMonitor.h \
    src/graphics/TargetItem.h \
    src/common/Target.h \
    src/common/Constants.h

# 资源文件
#RESOURCES += resources/resources.qrc

# 输出目录配置
DESTDIR = $${OUT_PWD}/bin
OBJECTS_DIR = $${OUT_PWD}/tmp
MOC_DIR = $${OUT_PWD}/tmp
RCC_DIR = $${OUT_PWD}/tmp
UI_DIR = $${OUT_PWD}/tmp

# 包含路径
INCLUDEPATH += src

# 编译器配置
win32 {
    QMAKE_CXXFLAGS += /std:c++17
}
unix {
    QMAKE_CXXFLAGS += -std=c++17
}

# 调试配置
CONFIG(debug, debug|release) {
    DEFINES += DEBUG_MODE
    TARGET = $${TARGET}_debug
}

# 发布配置
CONFIG(release, debug|release) {
    DEFINES += QT_NO_DEBUG_OUTPUT
}
