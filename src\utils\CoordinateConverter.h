#ifndef COORDINATECONVERTER_H
#define COORDINATECONVERTER_H

#include <QPointF>
#include <QRectF>

/**
 * @brief 坐标转换工具类
 * 
 * 提供各种坐标系统之间的转换功能：
 * - WGS84经纬度坐标
 * - 瓦片坐标(XYZ)
 * - 场景坐标(像素)
 */
class CoordinateConverter
{
public:
    CoordinateConverter();
    
    // 经纬度与瓦片坐标转换
    static QPointF lonLatToTile(double longitude, double latitude, int zoom);
    static QPointF tileToLonLat(double tileX, double tileY, int zoom);
    
    // 瓦片坐标与场景坐标转换
    static QPointF tileToScene(double tileX, double tileY, int zoom);
    static QPointF sceneToTile(double sceneX, double sceneY, int zoom);
    
    // 经纬度与场景坐标直接转换
    static QPointF lonLatToScene(double longitude, double latitude, int zoom);
    static QPointF sceneToLonLat(double sceneX, double sceneY, int zoom);
    
    // 计算可见瓦片范围
    static QRect calculateTileRect(const QRectF& sceneRect, int zoom);
    
    // 计算两点间距离(米)
    static double distanceMeters(double lat1, double lon1, double lat2, double lon2);
    
    // 计算方位角(度)
    static double bearing(double lat1, double lon1, double lat2, double lon2);
    
    // 边界检查
    static bool isValidLatitude(double latitude);
    static bool isValidLongitude(double longitude);
    static bool isValidZoomLevel(int zoom);
    
    // 常量
    static const double EARTH_RADIUS;
    static const double DEG_TO_RAD;
    static const double RAD_TO_DEG;

private:
    // 辅助函数
    static double sinh(double x);
    static double asinh(double x);
    static double atanh(double x);
};

#endif // COORDINATECONVERTER_H
