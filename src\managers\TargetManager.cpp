#include "TargetManager.h"
#include "models/TargetModel.h"
#include "common/Constants.h"
#include <QJsonObject>
#include <QDateTime>
#include <QDebug>

// 常量定义
const int TargetManager::DEFAULT_TIMEOUT_SECONDS = 300; // 5分钟
const int TargetManager::TIMEOUT_CHECK_INTERVAL = 30000; // 30秒

TargetManager::TargetManager(QObject *parent)
    : QObject(parent)
    , m_model(new TargetModel(this))
    , m_timeoutTimer(new QTimer(this))
    , m_targetTimeoutSeconds(DEFAULT_TIMEOUT_SECONDS)
    , m_autoCleanupEnabled(true)
{
    // 设置超时检查定时器
    m_timeoutTimer->setInterval(TIMEOUT_CHECK_INTERVAL);
    connect(m_timeoutTimer, &QTimer::timeout, this, &TargetManager::checkTargetTimeouts);
    
    startTimeoutTimer();
    
    qDebug() << "TargetManager initialized with timeout:" << m_targetTimeoutSeconds << "seconds";
}

TargetManager::~TargetManager()
{
    clearAllTargets();
}

void TargetManager::addTarget(const Target& target)
{
    if (target.id.isEmpty() || !target.isValid()) {
        qWarning() << "Invalid target data, cannot add target";
        return;
    }
    
    // 检查是否已存在
    if (m_targets.contains(target.id)) {
        qDebug() << "Target already exists, updating instead:" << target.id;
        Target* existingTarget = m_targets[target.id];
        *existingTarget = target;
        existingTarget->lastUpdate = QDateTime::currentDateTime();
        m_model->updateTarget(existingTarget);
        emit targetUpdated(target.id);
        return;
    }
    
    // 创建新目标
    Target* newTarget = new Target(target);
    newTarget->lastUpdate = QDateTime::currentDateTime();
    
    m_targets[target.id] = newTarget;
    m_model->addTarget(newTarget);
    
    emit targetAdded(target.id);
    emit targetCountChanged(m_targets.size());
    
    qDebug() << "Target added:" << target.id;
}

void TargetManager::updateTarget(const QString& targetId, const QJsonObject& data)
{
    Target* target = m_targets.value(targetId);
    if (!target) {
        // 如果目标不存在，尝试从JSON数据创建新目标
        if (data.contains("latitude") && data.contains("longitude")) {
            Target newTarget = Target::fromJson(data);
            if (!newTarget.id.isEmpty()) {
                addTarget(newTarget);
            }
        }
        return;
    }
    
    updateTargetFromJson(target, data);
    target->lastUpdate = QDateTime::currentDateTime();
    
    m_model->updateTarget(target);
    emit targetUpdated(targetId);
    
    qDebug() << "Target updated:" << targetId;
}

void TargetManager::removeTarget(const QString& targetId)
{
    Target* target = m_targets.take(targetId);
    if (target) {
        m_model->removeTarget(target);
        delete target;
        
        emit targetRemoved(targetId);
        emit targetCountChanged(m_targets.size());
        
        qDebug() << "Target removed:" << targetId;
    }
}

void TargetManager::clearAllTargets()
{
    QStringList targetIds = m_targets.keys();
    
    m_model->clearAllTargets();
    
    qDeleteAll(m_targets);
    m_targets.clear();
    
    for (const QString& targetId : targetIds) {
        emit targetRemoved(targetId);
    }
    
    emit targetCountChanged(0);
    
    qDebug() << "All targets cleared";
}

Target* TargetManager::getTarget(const QString& targetId) const
{
    return m_targets.value(targetId);
}

QList<Target*> TargetManager::getAllTargets() const
{
    return m_targets.values();
}

QStringList TargetManager::getTargetIds() const
{
    return m_targets.keys();
}

int TargetManager::getTargetCount() const
{
    return m_targets.size();
}

TargetModel* TargetManager::model() const
{
    return m_model;
}

void TargetManager::setTargetTimeout(int seconds)
{
    m_targetTimeoutSeconds = qMax(0, seconds);
    qDebug() << "Target timeout set to:" << m_targetTimeoutSeconds << "seconds";
}

int TargetManager::getTargetTimeout() const
{
    return m_targetTimeoutSeconds;
}

void TargetManager::setAutoCleanup(bool enabled)
{
    m_autoCleanupEnabled = enabled;
    
    if (enabled) {
        startTimeoutTimer();
    } else {
        stopTimeoutTimer();
    }
    
    qDebug() << "Auto cleanup" << (enabled ? "enabled" : "disabled");
}

bool TargetManager::isAutoCleanupEnabled() const
{
    return m_autoCleanupEnabled;
}

void TargetManager::checkTargetTimeouts()
{
    if (!m_autoCleanupEnabled || m_targetTimeoutSeconds <= 0) {
        return;
    }
    
    QDateTime now = QDateTime::currentDateTime();
    QStringList expiredTargets;
    
    for (auto it = m_targets.begin(); it != m_targets.end(); ++it) {
        Target* target = it.value();
        int secondsSinceUpdate = target->lastUpdate.secsTo(now);
        
        if (secondsSinceUpdate > m_targetTimeoutSeconds) {
            expiredTargets.append(target->id);
        }
    }
    
    // 移除过期目标
    for (const QString& targetId : expiredTargets) {
        emit targetTimeout(targetId);
        removeTarget(targetId);
    }
    
    if (!expiredTargets.isEmpty()) {
        qDebug() << "Removed" << expiredTargets.size() << "expired targets";
    }
}

void TargetManager::onTargetDataReceived(const QString& targetId, const QJsonObject& data)
{
    updateTarget(targetId, data);
}

void TargetManager::updateTargetFromJson(Target* target, const QJsonObject& data)
{
    if (!target) return;
    
    // 更新基本信息
    if (data.contains("name")) {
        target->name = data["name"].toString();
    }
    
    // 更新位置信息
    if (data.contains("latitude")) {
        target->latitude = data["latitude"].toDouble();
    }
    if (data.contains("longitude")) {
        target->longitude = data["longitude"].toDouble();
    }
    if (data.contains("altitude")) {
        target->altitude = data["altitude"].toDouble();
    }
    
    // 更新运动信息
    if (data.contains("heading")) {
        target->heading = data["heading"].toDouble();
    }
    if (data.contains("speed")) {
        target->speed = data["speed"].toDouble();
    }
    
    // 更新状态信息
    if (data.contains("type")) {
        target->type = data["type"].toString();
    }
    if (data.contains("status")) {
        target->status = data["status"].toString();
    }
    if (data.contains("iconPath")) {
        target->iconPath = data["iconPath"].toString();
    }
    
    // 更新扩展属性
    if (data.contains("properties")) {
        target->properties = data["properties"].toObject();
    }
}

void TargetManager::emitTargetUpdated(const QString& targetId)
{
    emit targetUpdated(targetId);
}

void TargetManager::startTimeoutTimer()
{
    if (m_autoCleanupEnabled && !m_timeoutTimer->isActive()) {
        m_timeoutTimer->start();
        qDebug() << "Target timeout timer started";
    }
}

void TargetManager::stopTimeoutTimer()
{
    if (m_timeoutTimer->isActive()) {
        m_timeoutTimer->stop();
        qDebug() << "Target timeout timer stopped";
    }
}
